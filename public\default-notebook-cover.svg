<svg width="400" height="200" viewBox="0 0 400 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Paper texture gradient -->
    <linearGradient id="paperGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#f1f5f9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>

    <!-- Leather texture gradient -->
    <linearGradient id="leatherGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b4513;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#a0522d;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#8b4513;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#654321;stop-opacity:1" />
    </linearGradient>

    <!-- Shadow gradient -->
    <linearGradient id="shadowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#000000;stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:#000000;stop-opacity:0.3" />
    </linearGradient>

    <!-- Subtle pattern -->
    <pattern id="paperPattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
      <rect width="20" height="20" fill="none"/>
      <circle cx="10" cy="10" r="0.5" fill="rgba(148,163,184,0.1)"/>
    </pattern>
  </defs>

  <!-- Background shadow -->
  <rect x="5" y="5" width="390" height="190" rx="8" fill="url(#shadowGradient)"/>

  <!-- Notebook cover (leather-like) -->
  <rect x="0" y="0" width="390" height="185" rx="8" fill="url(#leatherGradient)" stroke="#654321" stroke-width="1"/>

  <!-- Notebook pages (visible from side) -->
  <rect x="385" y="8" width="8" height="169" fill="#f8fafc" stroke="#e2e8f0" stroke-width="0.5"/>
  <rect x="387" y="10" width="6" height="165" fill="#ffffff"/>

  <!-- Cover texture overlay -->
  <rect x="0" y="0" width="390" height="185" rx="8" fill="url(#paperPattern)" opacity="0.3"/>

  <!-- Embossed title area -->
  <rect x="30" y="40" width="330" height="80" rx="4" fill="rgba(0,0,0,0.1)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
  <rect x="32" y="42" width="326" height="76" rx="3" fill="rgba(255,255,255,0.05)"/>

  <!-- Title text -->
  <text x="195" y="75" text-anchor="middle" fill="#f8fafc" font-family="Georgia, serif" font-size="18" font-weight="bold" letter-spacing="1px">
    MY NOTEBOOK
  </text>

  <!-- Subtitle -->
  <text x="195" y="95" text-anchor="middle" fill="rgba(248,250,252,0.8)" font-family="Georgia, serif" font-size="12" font-style="italic">
    Ideas • Notes • Thoughts
  </text>

  <!-- Decorative corner elements -->
  <g stroke="rgba(248,250,252,0.3)" stroke-width="1" fill="none">
    <!-- Top left corner -->
    <path d="M 20 20 L 35 20 M 20 20 L 20 35"/>
    <!-- Top right corner -->
    <path d="M 370 20 L 355 20 M 370 20 L 370 35"/>
    <!-- Bottom left corner -->
    <path d="M 20 165 L 35 165 M 20 165 L 20 150"/>
    <!-- Bottom right corner -->
    <path d="M 370 165 L 355 165 M 370 165 L 370 150"/>
  </g>

  <!-- Subtle highlight on top edge -->
  <rect x="0" y="0" width="390" height="3" rx="8" fill="rgba(255,255,255,0.2)"/>

  <!-- Binding rings/spiral effect -->
  <g fill="rgba(101,67,33,0.8)">
    <circle cx="50" cy="30" r="2"/>
    <circle cx="50" cy="50" r="2"/>
    <circle cx="50" cy="70" r="2"/>
    <circle cx="50" cy="90" r="2"/>
    <circle cx="50" cy="110" r="2"/>
    <circle cx="50" cy="130" r="2"/>
    <circle cx="50" cy="150" r="2"/>
  </g>

  <!-- Small embossed logo/icon -->
  <g transform="translate(320, 140)">
    <circle cx="0" cy="0" r="12" fill="rgba(0,0,0,0.1)"/>
    <circle cx="0" cy="0" r="10" fill="rgba(255,255,255,0.1)" stroke="rgba(248,250,252,0.3)" stroke-width="1"/>
    <path d="M -4 -2 L 4 -2 L 4 6 L -4 6 Z" fill="rgba(248,250,252,0.6)"/>
    <line x1="-2" y1="0" x2="2" y2="0" stroke="rgba(139,69,19,0.4)" stroke-width="0.5"/>
    <line x1="-2" y1="2" x2="2" y2="2" stroke="rgba(139,69,19,0.4)" stroke-width="0.5"/>
    <line x1="-2" y1="4" x2="2" y2="4" stroke="rgba(139,69,19,0.4)" stroke-width="0.5"/>
  </g>
</svg>
