import React, { useEffect, useState } from "react";
import { use<PERSON>ara<PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { supabase } from "@/lib/supabaseClient";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ArrowLeft,
  Search,
  Plus,
  FileText,
  Calendar,
  Tag,
  SortAsc,
  SortDesc,
  Star,
  Lock,
  Pin,
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/AuthContext";

export default function NotebookDetailsPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { currentUser } = useAuth();

  const [notebook, setNotebook] = useState(null);
  const [notes, setNotes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [notesError, setNotesError] = useState(null);

  // Search and filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("updated_at");
  const [sortOrder, setSortOrder] = useState("desc");
  const [filterBy, setFilterBy] = useState("all");

  useEffect(() => {
    const fetchNotebookAndNotes = async () => {
      if (!currentUser || !id) {
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);
      setNotesError(null);

      try {
        // Fetch notebook with user verification
        const { data: nb, error: nbError } = await supabase
          .from("notebooks")
          .select("*")
          .eq("id", id)
          .eq("user_id", currentUser.id)
          .single();

        if (nbError) {
          setNotebook(null);
          setError("Notebook not found or you don't have access to it.");
          setLoading(false);
          return;
        }

        // Fetch notes with sorting
        let notesQuery = supabase
          .from("notes")
          .select("*")
          .eq("notebook_id", id)
          .eq("user_id", currentUser.id)
          .eq("is_trashed", false);

        // Apply sorting
        const ascending = sortOrder === "asc";
        notesQuery = notesQuery.order(sortBy, { ascending });

        const { data: notesData, error: notesErrorObj } = await notesQuery;

        if (notesErrorObj) {
          setNotes([]);
          setNotesError("Failed to load notes: " + notesErrorObj.message);
        } else {
          setNotes(notesData || []);
        }

        setNotebook(nb);
      } catch (error) {
        console.error("Error fetching notebook data:", error);
        setError("Failed to load notebook data.");
        toast({
          title: "Error",
          description: "Failed to load notebook data: " + error.message,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchNotebookAndNotes();
  }, [id, currentUser, sortBy, sortOrder, toast]);

  // Filter notes based on search term and filters
  const filteredNotes = notes.filter((note) => {
    const term = searchTerm.toLowerCase();

    // Search filter
    const matchesSearch =
      !term ||
      (note.title && note.title.toLowerCase().includes(term)) ||
      (note.content_html && note.content_html.toLowerCase().includes(term)) ||
      (note.tags && note.tags.some((tag) => tag.toLowerCase().includes(term)));

    // Type filter
    let matchesFilter = true;
    if (filterBy === "favorites") {
      matchesFilter = note.is_favorite;
    } else if (filterBy === "locked") {
      matchesFilter = note.is_locked;
    } else if (filterBy === "sticky") {
      matchesFilter = note.is_sticky;
    }

    return matchesSearch && matchesFilter;
  });

  if (loading) {
    return (
      <div className="p-8 text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-slate-600 dark:text-slate-400">
          Loading notebook...
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8 text-center">
        <FileText className="mx-auto mb-4 h-16 w-16 text-slate-400" />
        <p className="text-xl text-slate-600 dark:text-slate-400 mb-2">
          {error}
        </p>
        <Button onClick={() => navigate("/notebooks")} variant="outline">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Notebooks
        </Button>
      </div>
    );
  }

  if (!notebook) {
    return (
      <div className="p-8 text-center">
        <FileText className="mx-auto mb-4 h-16 w-16 text-slate-400" />
        <p className="text-xl text-slate-600 dark:text-slate-400">
          Notebook not found.
        </p>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="max-w-5xl mx-auto p-4"
    >
      {/* Header */}
      <div className="mb-6 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex items-center gap-3">
          <Link to="/notebooks">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div>
            <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-100">
              {notebook.title}
            </h2>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              {filteredNotes.length}{" "}
              {filteredNotes.length === 1 ? "note" : "notes"}
            </p>
          </div>
        </div>
        <Button
          onClick={() => navigate(`/notes/new?notebook=${id}`)}
          className="bg-primary hover:bg-primary/90 text-primary-foreground"
        >
          <Plus className="mr-2 h-4 w-4" />
          New Note
        </Button>
      </div>

      {/* Notebook Info */}
      <div className="mb-6 p-4 rounded-xl bg-slate-100 dark:bg-slate-800/60 flex items-center gap-6">
        {/* Always show notebook image */}
        <img
          src={notebook.cover_image || "/default-notebook-cover.png"}
          alt="Notebook Cover"
          className="w-20 h-20 object-cover rounded-lg border border-slate-200 dark:border-slate-700"
          onError={(e) => {
            e.target.src = "/default-notebook-cover.png";
          }}
        />
        <div className="flex-1">
          <p className="text-slate-700 dark:text-slate-200 mb-3">
            {notebook.description}
          </p>
          <div className="flex flex-wrap gap-2 mb-3">
            {notebook.tags &&
              notebook.tags.map((tag, idx) => (
                <span
                  key={idx}
                  className="inline-flex items-center text-xs bg-slate-200 dark:bg-slate-700 text-slate-600 dark:text-slate-300 rounded-full px-2 py-1"
                >
                  <Tag className="mr-1 h-3 w-3" />
                  {tag}
                </span>
              ))}
          </div>
          <div className="flex items-center gap-4 text-xs text-slate-500 dark:text-slate-400">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              Created:{" "}
              {notebook.created_at
                ? new Date(notebook.created_at).toLocaleDateString()
                : "-"}
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              Updated:{" "}
              {notebook.updated_at
                ? new Date(notebook.updated_at).toLocaleDateString()
                : "-"}
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filter Controls */}
      <div className="mb-6 p-4 bg-slate-100 dark:bg-slate-800/60 rounded-xl">
        <div className="flex flex-col gap-4">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              type="search"
              placeholder="Search notes by title, content, or tags..."
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {/* Filters and Sorting */}
          <div className="flex flex-col sm:flex-row gap-3 items-center">
            <div className="flex items-center gap-2">
              <Select value={filterBy} onValueChange={setFilterBy}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Filter" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Notes</SelectItem>
                  <SelectItem value="favorites">Favorites</SelectItem>
                  <SelectItem value="locked">Locked</SelectItem>
                  <SelectItem value="sticky">Sticky</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-36">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="updated_at">Last Updated</SelectItem>
                  <SelectItem value="created_at">Date Created</SelectItem>
                  <SelectItem value="title">Title</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
              className="flex items-center gap-1"
            >
              {sortOrder === "asc" ? (
                <SortAsc className="h-4 w-4" />
              ) : (
                <SortDesc className="h-4 w-4" />
              )}
              {sortOrder === "asc" ? "Ascending" : "Descending"}
            </Button>
          </div>
        </div>
      </div>
      {/* Notes Section */}
      {notesError && (
        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-red-600 dark:text-red-400 text-sm">{notesError}</p>
        </div>
      )}

      {filteredNotes.length === 0 ? (
        <div className="text-center py-10">
          <FileText className="mx-auto mb-4 h-16 w-16 text-slate-400" />
          <p className="text-xl text-slate-600 dark:text-slate-400 mb-2">
            {searchTerm || filterBy !== "all"
              ? "No notes match your search"
              : "No notes in this notebook yet"}
          </p>
          <p className="text-slate-500 dark:text-slate-500 mb-4">
            {searchTerm || filterBy !== "all"
              ? "Try adjusting your search terms or filters"
              : "Create your first note to get started!"}
          </p>
          {!searchTerm && filterBy === "all" && (
            <Button onClick={() => navigate(`/notes/new?notebook=${id}`)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Note
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {filteredNotes.map((note) => (
            <motion.div
              key={note.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card
                className="hover:shadow-lg transition-all duration-200 cursor-pointer group"
                onClick={() => navigate(`/notes/${note.id}`)}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-lg line-clamp-1 group-hover:text-primary transition-colors">
                      {note.title || "Untitled Note"}
                    </CardTitle>
                    <div className="flex items-center gap-1 ml-2">
                      {note.is_favorite && (
                        <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      )}
                      {note.is_locked && (
                        <Lock className="h-4 w-4 text-red-500" />
                      )}
                      {note.is_sticky && (
                        <Pin className="h-4 w-4 text-blue-500" />
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  {/* Tags */}
                  {note.tags && note.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mb-3">
                      {note.tags.slice(0, 3).map((tag, idx) => (
                        <span
                          key={idx}
                          className="inline-flex items-center text-xs bg-slate-200 dark:bg-slate-700 text-slate-600 dark:text-slate-300 rounded px-2 py-0.5"
                        >
                          {tag}
                        </span>
                      ))}
                      {note.tags.length > 3 && (
                        <span className="text-xs text-slate-500">
                          +{note.tags.length - 3} more
                        </span>
                      )}
                    </div>
                  )}

                  {/* Content Preview */}
                  <div className="line-clamp-3 text-slate-700 dark:text-slate-200 text-sm mb-3">
                    {note.content_html
                      ? note.content_html.replace(/<[^>]*>/g, "")
                      : "No content"}
                  </div>

                  {/* Metadata */}
                  <div className="flex items-center justify-between text-xs text-slate-500 dark:text-slate-400">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {note.updated_at
                        ? new Date(note.updated_at).toLocaleDateString()
                        : "-"}
                    </div>
                    <div className="flex items-center gap-2">
                      {note.reminder_at && (
                        <span className="text-orange-500">Reminder set</span>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      )}
    </motion.div>
  );
}
