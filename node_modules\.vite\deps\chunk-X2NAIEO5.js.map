{"version": 3, "sources": ["../../@radix-ui/react-popper/src/popper.tsx", "../../@floating-ui/utils/dist/floating-ui.utils.mjs", "../../@floating-ui/core/dist/floating-ui.core.mjs", "../../@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "../../@floating-ui/dom/dist/floating-ui.dom.mjs", "../../@floating-ui/react-dom/dist/floating-ui.react-dom.mjs", "../../@radix-ui/react-arrow/src/arrow.tsx"], "sourcesContent": ["import * as React from 'react';\nimport {\n  useFloating,\n  autoUpdate,\n  offset,\n  shift,\n  limitShift,\n  hide,\n  arrow as floatingUIarrow,\n  flip,\n  size,\n} from '@floating-ui/react-dom';\nimport * as ArrowPrimitive from '@radix-ui/react-arrow';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useSize } from '@radix-ui/react-use-size';\n\nimport type { Placement, Middleware } from '@floating-ui/react-dom';\nimport type { Scope } from '@radix-ui/react-context';\nimport type { Measurable } from '@radix-ui/rect';\n\nconst SIDE_OPTIONS = ['top', 'right', 'bottom', 'left'] as const;\nconst ALIGN_OPTIONS = ['start', 'center', 'end'] as const;\n\ntype Side = (typeof SIDE_OPTIONS)[number];\ntype Align = (typeof ALIGN_OPTIONS)[number];\n\n/* -------------------------------------------------------------------------------------------------\n * Popper\n * -----------------------------------------------------------------------------------------------*/\n\nconst POPPER_NAME = 'Popper';\n\ntype ScopedProps<P> = P & { __scopePopper?: Scope };\nconst [createPopperContext, createPopperScope] = createContextScope(POPPER_NAME);\n\ntype PopperContextValue = {\n  anchor: Measurable | null;\n  onAnchorChange(anchor: Measurable | null): void;\n};\nconst [PopperProvider, usePopperContext] = createPopperContext<PopperContextValue>(POPPER_NAME);\n\ninterface PopperProps {\n  children?: React.ReactNode;\n}\nconst Popper: React.FC<PopperProps> = (props: ScopedProps<PopperProps>) => {\n  const { __scopePopper, children } = props;\n  const [anchor, setAnchor] = React.useState<Measurable | null>(null);\n  return (\n    <PopperProvider scope={__scopePopper} anchor={anchor} onAnchorChange={setAnchor}>\n      {children}\n    </PopperProvider>\n  );\n};\n\nPopper.displayName = POPPER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperAnchor\n * -----------------------------------------------------------------------------------------------*/\n\nconst ANCHOR_NAME = 'PopperAnchor';\n\ntype PopperAnchorElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PopperAnchorProps extends PrimitiveDivProps {\n  virtualRef?: React.RefObject<Measurable>;\n}\n\nconst PopperAnchor = React.forwardRef<PopperAnchorElement, PopperAnchorProps>(\n  (props: ScopedProps<PopperAnchorProps>, forwardedRef) => {\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = React.useRef<PopperAnchorElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, ref);\n\n    React.useEffect(() => {\n      // Consumer can anchor the popper to something that isn't\n      // a DOM node e.g. pointer position, so we override the\n      // `anchorRef` with their virtual ref in this case.\n      context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n\n    return virtualRef ? null : <Primitive.div {...anchorProps} ref={composedRefs} />;\n  }\n);\n\nPopperAnchor.displayName = ANCHOR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'PopperContent';\n\ntype PopperContentContextValue = {\n  placedSide: Side;\n  onArrowChange(arrow: HTMLSpanElement | null): void;\n  arrowX?: number;\n  arrowY?: number;\n  shouldHideArrow: boolean;\n};\n\nconst [PopperContentProvider, useContentContext] =\n  createPopperContext<PopperContentContextValue>(CONTENT_NAME);\n\ntype Boundary = Element | null;\n\ntype PopperContentElement = React.ElementRef<typeof Primitive.div>;\ninterface PopperContentProps extends PrimitiveDivProps {\n  side?: Side;\n  sideOffset?: number;\n  align?: Align;\n  alignOffset?: number;\n  arrowPadding?: number;\n  avoidCollisions?: boolean;\n  collisionBoundary?: Boundary | Boundary[];\n  collisionPadding?: number | Partial<Record<Side, number>>;\n  sticky?: 'partial' | 'always';\n  hideWhenDetached?: boolean;\n  updatePositionStrategy?: 'optimized' | 'always';\n  onPlaced?: () => void;\n}\n\nconst PopperContent = React.forwardRef<PopperContentElement, PopperContentProps>(\n  (props: ScopedProps<PopperContentProps>, forwardedRef) => {\n    const {\n      __scopePopper,\n      side = 'bottom',\n      sideOffset = 0,\n      align = 'center',\n      alignOffset = 0,\n      arrowPadding = 0,\n      avoidCollisions = true,\n      collisionBoundary = [],\n      collisionPadding: collisionPaddingProp = 0,\n      sticky = 'partial',\n      hideWhenDetached = false,\n      updatePositionStrategy = 'optimized',\n      onPlaced,\n      ...contentProps\n    } = props;\n\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n\n    const [content, setContent] = React.useState<HTMLDivElement | null>(null);\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setContent(node));\n\n    const [arrow, setArrow] = React.useState<HTMLSpanElement | null>(null);\n    const arrowSize = useSize(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n\n    const desiredPlacement = (side + (align !== 'center' ? '-' + align : '')) as Placement;\n\n    const collisionPadding =\n      typeof collisionPaddingProp === 'number'\n        ? collisionPaddingProp\n        : { top: 0, right: 0, bottom: 0, left: 0, ...collisionPaddingProp };\n\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n    const hasExplicitBoundaries = boundary.length > 0;\n\n    const detectOverflowOptions = {\n      padding: collisionPadding,\n      boundary: boundary.filter(isNotNull),\n      // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n      altBoundary: hasExplicitBoundaries,\n    };\n\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = useFloating({\n      // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n      strategy: 'fixed',\n      placement: desiredPlacement,\n      whileElementsMounted: (...args) => {\n        const cleanup = autoUpdate(...args, {\n          animationFrame: updatePositionStrategy === 'always',\n        });\n        return cleanup;\n      },\n      elements: {\n        reference: context.anchor,\n      },\n      middleware: [\n        offset({ mainAxis: sideOffset + arrowHeight, alignmentAxis: alignOffset }),\n        avoidCollisions &&\n          shift({\n            mainAxis: true,\n            crossAxis: false,\n            limiter: sticky === 'partial' ? limitShift() : undefined,\n            ...detectOverflowOptions,\n          }),\n        avoidCollisions && flip({ ...detectOverflowOptions }),\n        size({\n          ...detectOverflowOptions,\n          apply: ({ elements, rects, availableWidth, availableHeight }) => {\n            const { width: anchorWidth, height: anchorHeight } = rects.reference;\n            const contentStyle = elements.floating.style;\n            contentStyle.setProperty('--radix-popper-available-width', `${availableWidth}px`);\n            contentStyle.setProperty('--radix-popper-available-height', `${availableHeight}px`);\n            contentStyle.setProperty('--radix-popper-anchor-width', `${anchorWidth}px`);\n            contentStyle.setProperty('--radix-popper-anchor-height', `${anchorHeight}px`);\n          },\n        }),\n        arrow && floatingUIarrow({ element: arrow, padding: arrowPadding }),\n        transformOrigin({ arrowWidth, arrowHeight }),\n        hideWhenDetached && hide({ strategy: 'referenceHidden', ...detectOverflowOptions }),\n      ],\n    });\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n\n    const handlePlaced = useCallbackRef(onPlaced);\n    useLayoutEffect(() => {\n      if (isPositioned) {\n        handlePlaced?.();\n      }\n    }, [isPositioned, handlePlaced]);\n\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n\n    const [contentZIndex, setContentZIndex] = React.useState<string>();\n    useLayoutEffect(() => {\n      if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [content]);\n\n    return (\n      <div\n        ref={refs.setFloating}\n        data-radix-popper-content-wrapper=\"\"\n        style={{\n          ...floatingStyles,\n          transform: isPositioned ? floatingStyles.transform : 'translate(0, -200%)', // keep off the page when measuring\n          minWidth: 'max-content',\n          zIndex: contentZIndex,\n          ['--radix-popper-transform-origin' as any]: [\n            middlewareData.transformOrigin?.x,\n            middlewareData.transformOrigin?.y,\n          ].join(' '),\n\n          // hide the content if using the hide middleware and should be hidden\n          // set visibility to hidden and disable pointer events so the UI behaves\n          // as if the PopperContent isn't there at all\n          ...(middlewareData.hide?.referenceHidden && {\n            visibility: 'hidden',\n            pointerEvents: 'none',\n          }),\n        }}\n        // Floating UI interally calculates logical alignment based the `dir` attribute on\n        // the reference/floating node, we must add this attribute here to ensure\n        // this is calculated when portalled as well as inline.\n        dir={props.dir}\n      >\n        <PopperContentProvider\n          scope={__scopePopper}\n          placedSide={placedSide}\n          onArrowChange={setArrow}\n          arrowX={arrowX}\n          arrowY={arrowY}\n          shouldHideArrow={cannotCenterArrow}\n        >\n          <Primitive.div\n            data-side={placedSide}\n            data-align={placedAlign}\n            {...contentProps}\n            ref={composedRefs}\n            style={{\n              ...contentProps.style,\n              // if the PopperContent hasn't been placed yet (not all measurements done)\n              // we prevent animations so that users's animation don't kick in too early referring wrong sides\n              animation: !isPositioned ? 'none' : undefined,\n            }}\n          />\n        </PopperContentProvider>\n      </div>\n    );\n  }\n);\n\nPopperContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * PopperArrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst ARROW_NAME = 'PopperArrow';\n\nconst OPPOSITE_SIDE: Record<Side, Side> = {\n  top: 'bottom',\n  right: 'left',\n  bottom: 'top',\n  left: 'right',\n};\n\ntype PopperArrowElement = React.ElementRef<typeof ArrowPrimitive.Root>;\ntype ArrowProps = React.ComponentPropsWithoutRef<typeof ArrowPrimitive.Root>;\ninterface PopperArrowProps extends ArrowProps {}\n\nconst PopperArrow = React.forwardRef<PopperArrowElement, PopperArrowProps>(function PopperArrow(\n  props: ScopedProps<PopperArrowProps>,\n  forwardedRef\n) {\n  const { __scopePopper, ...arrowProps } = props;\n  const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n  const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n\n  return (\n    // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    <span\n      ref={contentContext.onArrowChange}\n      style={{\n        position: 'absolute',\n        left: contentContext.arrowX,\n        top: contentContext.arrowY,\n        [baseSide]: 0,\n        transformOrigin: {\n          top: '',\n          right: '0 0',\n          bottom: 'center 0',\n          left: '100% 0',\n        }[contentContext.placedSide],\n        transform: {\n          top: 'translateY(100%)',\n          right: 'translateY(50%) rotate(90deg) translateX(-50%)',\n          bottom: `rotate(180deg)`,\n          left: 'translateY(50%) rotate(-90deg) translateX(50%)',\n        }[contentContext.placedSide],\n        visibility: contentContext.shouldHideArrow ? 'hidden' : undefined,\n      }}\n    >\n      <ArrowPrimitive.Root\n        {...arrowProps}\n        ref={forwardedRef}\n        style={{\n          ...arrowProps.style,\n          // ensures the element can be measured correctly (mostly for if SVG)\n          display: 'block',\n        }}\n      />\n    </span>\n  );\n});\n\nPopperArrow.displayName = ARROW_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction isNotNull<T>(value: T | null): value is T {\n  return value !== null;\n}\n\nconst transformOrigin = (options: { arrowWidth: number; arrowHeight: number }): Middleware => ({\n  name: 'transformOrigin',\n  options,\n  fn(data) {\n    const { placement, rects, middlewareData } = data;\n\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = { start: '0%', center: '50%', end: '100%' }[placedAlign];\n\n    const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n    const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n\n    let x = '';\n    let y = '';\n\n    if (placedSide === 'bottom') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === 'top') {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === 'right') {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === 'left') {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return { data: { x, y } };\n  },\n});\n\nfunction getSideAndAlignFromPlacement(placement: Placement) {\n  const [side, align = 'center'] = placement.split('-');\n  return [side as Side, align as Align] as const;\n}\n\nconst Root = Popper;\nconst Anchor = PopperAnchor;\nconst Content = PopperContent;\nconst Arrow = PopperArrow;\n\nexport {\n  createPopperScope,\n  //\n  Popper,\n  PopperAnchor,\n  PopperContent,\n  PopperArrow,\n  //\n  Root,\n  Anchor,\n  Content,\n  Arrow,\n  //\n  SIDE_OPTIONS,\n  ALIGN_OPTIONS,\n};\nexport type { PopperProps, PopperAnchorProps, PopperContentProps, PopperArrowProps };\n", "/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */\n\nconst sides = ['top', 'right', 'bottom', 'left'];\nconst alignments = ['start', 'end'];\nconst placements = /*#__PURE__*/sides.reduce((acc, side) => acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = v => ({\n  x: v,\n  y: v\n});\nconst oppositeSideMap = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nconst oppositeAlignmentMap = {\n  start: 'end',\n  end: 'start'\n};\nfunction clamp(start, value, end) {\n  return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n  return typeof value === 'function' ? value(param) : value;\n}\nfunction getSide(placement) {\n  return placement.split('-')[0];\n}\nfunction getAlignment(placement) {\n  return placement.split('-')[1];\n}\nfunction getOppositeAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}\nfunction getAxisLength(axis) {\n  return axis === 'y' ? 'height' : 'width';\n}\nfunction getSideAxis(placement) {\n  return ['top', 'bottom'].includes(getSide(placement)) ? 'y' : 'x';\n}\nfunction getAlignmentAxis(placement) {\n  return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n  if (rtl === void 0) {\n    rtl = false;\n  }\n  const alignment = getAlignment(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const length = getAxisLength(alignmentAxis);\n  let mainAlignmentSide = alignmentAxis === 'x' ? alignment === (rtl ? 'end' : 'start') ? 'right' : 'left' : alignment === 'start' ? 'bottom' : 'top';\n  if (rects.reference[length] > rects.floating[length]) {\n    mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n  }\n  return [mainAlignmentSide, getOppositePlacement(mainAlignmentSide)];\n}\nfunction getExpandedPlacements(placement) {\n  const oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeAlignmentPlacement(placement), oppositePlacement, getOppositeAlignmentPlacement(oppositePlacement)];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n  return placement.replace(/start|end/g, alignment => oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n  const lr = ['left', 'right'];\n  const rl = ['right', 'left'];\n  const tb = ['top', 'bottom'];\n  const bt = ['bottom', 'top'];\n  switch (side) {\n    case 'top':\n    case 'bottom':\n      if (rtl) return isStart ? rl : lr;\n      return isStart ? lr : rl;\n    case 'left':\n    case 'right':\n      return isStart ? tb : bt;\n    default:\n      return [];\n  }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n  const alignment = getAlignment(placement);\n  let list = getSideList(getSide(placement), direction === 'start', rtl);\n  if (alignment) {\n    list = list.map(side => side + \"-\" + alignment);\n    if (flipAlignment) {\n      list = list.concat(list.map(getOppositeAlignmentPlacement));\n    }\n  }\n  return list;\n}\nfunction getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, side => oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0,\n    ...padding\n  };\n}\nfunction getPaddingObject(padding) {\n  return typeof padding !== 'number' ? expandPaddingObject(padding) : {\n    top: padding,\n    right: padding,\n    bottom: padding,\n    left: padding\n  };\n}\nfunction rectToClientRect(rect) {\n  const {\n    x,\n    y,\n    width,\n    height\n  } = rect;\n  return {\n    width,\n    height,\n    top: y,\n    left: x,\n    right: x + width,\n    bottom: y + height,\n    x,\n    y\n  };\n}\n\nexport { alignments, clamp, createCoords, evaluate, expandPaddingObject, floor, getAlignment, getAlignmentAxis, getAlignmentSides, getAxisLength, getExpandedPlacements, getOppositeAlignmentPlacement, getOppositeAxis, getOppositeAxisPlacements, getOppositePlacement, getPaddingObject, getSide, getSideAxis, max, min, placements, rectToClientRect, round, sides };\n", "import { getSideAxis, getAlignmentAxis, getAxisLength, getSide, getAlignment, evaluate, getPaddingObject, rectToClientRect, min, clamp, placements, getAlignmentSides, getOppositeAlignmentPlacement, getOppositePlacement, getExpandedPlacements, getOppositeAxisPlacements, sides, max, getOppositeAxis } from '@floating-ui/utils';\nexport { rectToClientRect } from '@floating-ui/utils';\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n  let {\n    reference,\n    floating\n  } = _ref;\n  const sideAxis = getSideAxis(placement);\n  const alignmentAxis = getAlignmentAxis(placement);\n  const alignLength = getAxisLength(alignmentAxis);\n  const side = getSide(placement);\n  const isVertical = sideAxis === 'y';\n  const commonX = reference.x + reference.width / 2 - floating.width / 2;\n  const commonY = reference.y + reference.height / 2 - floating.height / 2;\n  const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n  let coords;\n  switch (side) {\n    case 'top':\n      coords = {\n        x: commonX,\n        y: reference.y - floating.height\n      };\n      break;\n    case 'bottom':\n      coords = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case 'right':\n      coords = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case 'left':\n      coords = {\n        x: reference.x - floating.width,\n        y: commonY\n      };\n      break;\n    default:\n      coords = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  switch (getAlignment(placement)) {\n    case 'start':\n      coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n    case 'end':\n      coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n      break;\n  }\n  return coords;\n}\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */\nconst computePosition = async (reference, floating, config) => {\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform\n  } = config;\n  const validMiddleware = middleware.filter(Boolean);\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n  let rects = await platform.getElementRects({\n    reference,\n    floating,\n    strategy\n  });\n  let {\n    x,\n    y\n  } = computeCoordsFromPlacement(rects, placement, rtl);\n  let statefulPlacement = placement;\n  let middlewareData = {};\n  let resetCount = 0;\n  for (let i = 0; i < validMiddleware.length; i++) {\n    const {\n      name,\n      fn\n    } = validMiddleware[i];\n    const {\n      x: nextX,\n      y: nextY,\n      data,\n      reset\n    } = await fn({\n      x,\n      y,\n      initialPlacement: placement,\n      placement: statefulPlacement,\n      strategy,\n      middlewareData,\n      rects,\n      platform,\n      elements: {\n        reference,\n        floating\n      }\n    });\n    x = nextX != null ? nextX : x;\n    y = nextY != null ? nextY : y;\n    middlewareData = {\n      ...middlewareData,\n      [name]: {\n        ...middlewareData[name],\n        ...data\n      }\n    };\n    if (reset && resetCount <= 50) {\n      resetCount++;\n      if (typeof reset === 'object') {\n        if (reset.placement) {\n          statefulPlacement = reset.placement;\n        }\n        if (reset.rects) {\n          rects = reset.rects === true ? await platform.getElementRects({\n            reference,\n            floating,\n            strategy\n          }) : reset.rects;\n        }\n        ({\n          x,\n          y\n        } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n      }\n      i = -1;\n    }\n  }\n  return {\n    x,\n    y,\n    placement: statefulPlacement,\n    strategy,\n    middlewareData\n  };\n};\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nasync function detectOverflow(state, options) {\n  var _await$platform$isEle;\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    x,\n    y,\n    platform,\n    rects,\n    elements,\n    strategy\n  } = state;\n  const {\n    boundary = 'clippingAncestors',\n    rootBoundary = 'viewport',\n    elementContext = 'floating',\n    altBoundary = false,\n    padding = 0\n  } = evaluate(options, state);\n  const paddingObject = getPaddingObject(padding);\n  const altContext = elementContext === 'floating' ? 'reference' : 'floating';\n  const element = elements[altBoundary ? altContext : elementContext];\n  const clippingClientRect = rectToClientRect(await platform.getClippingRect({\n    element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || (await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating))),\n    boundary,\n    rootBoundary,\n    strategy\n  }));\n  const rect = elementContext === 'floating' ? {\n    x,\n    y,\n    width: rects.floating.width,\n    height: rects.floating.height\n  } : rects.reference;\n  const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n  const offsetScale = (await (platform.isElement == null ? void 0 : platform.isElement(offsetParent))) ? (await (platform.getScale == null ? void 0 : platform.getScale(offsetParent))) || {\n    x: 1,\n    y: 1\n  } : {\n    x: 1,\n    y: 1\n  };\n  const elementClientRect = rectToClientRect(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  }) : rect);\n  return {\n    top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n    bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n    left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n    right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n  };\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = options => ({\n  name: 'arrow',\n  options,\n  async fn(state) {\n    const {\n      x,\n      y,\n      placement,\n      rects,\n      platform,\n      elements,\n      middlewareData\n    } = state;\n    // Since `element` is required, we don't Partial<> the type.\n    const {\n      element,\n      padding = 0\n    } = evaluate(options, state) || {};\n    if (element == null) {\n      return {};\n    }\n    const paddingObject = getPaddingObject(padding);\n    const coords = {\n      x,\n      y\n    };\n    const axis = getAlignmentAxis(placement);\n    const length = getAxisLength(axis);\n    const arrowDimensions = await platform.getDimensions(element);\n    const isYAxis = axis === 'y';\n    const minProp = isYAxis ? 'top' : 'left';\n    const maxProp = isYAxis ? 'bottom' : 'right';\n    const clientProp = isYAxis ? 'clientHeight' : 'clientWidth';\n    const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n    const startDiff = coords[axis] - rects.reference[axis];\n    const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n    let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n\n    // DOM platform can return `window` as the `offsetParent`.\n    if (!clientSize || !(await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent)))) {\n      clientSize = elements.floating[clientProp] || rects.floating[length];\n    }\n    const centerToReference = endDiff / 2 - startDiff / 2;\n\n    // If the padding is large enough that it causes the arrow to no longer be\n    // centered, modify the padding so that it is centered.\n    const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n    const minPadding = min(paddingObject[minProp], largestPossiblePadding);\n    const maxPadding = min(paddingObject[maxProp], largestPossiblePadding);\n\n    // Make sure the arrow doesn't overflow the floating element if the center\n    // point is outside the floating element's bounds.\n    const min$1 = minPadding;\n    const max = clientSize - arrowDimensions[length] - maxPadding;\n    const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n    const offset = clamp(min$1, center, max);\n\n    // If the reference is small enough that the arrow's padding causes it to\n    // to point to nothing for an aligned placement, adjust the offset of the\n    // floating element itself. To ensure `shift()` continues to take action,\n    // a single reset is performed when this is true.\n    const shouldAddOffset = !middlewareData.arrow && getAlignment(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n    const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n    return {\n      [axis]: coords[axis] + alignmentOffset,\n      data: {\n        [axis]: offset,\n        centerOffset: center - offset - alignmentOffset,\n        ...(shouldAddOffset && {\n          alignmentOffset\n        })\n      },\n      reset: shouldAddOffset\n    };\n  }\n});\n\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n  const allowedPlacementsSortedByAlignment = alignment ? [...allowedPlacements.filter(placement => getAlignment(placement) === alignment), ...allowedPlacements.filter(placement => getAlignment(placement) !== alignment)] : allowedPlacements.filter(placement => getSide(placement) === placement);\n  return allowedPlacementsSortedByAlignment.filter(placement => {\n    if (alignment) {\n      return getAlignment(placement) === alignment || (autoAlignment ? getOppositeAlignmentPlacement(placement) !== placement : false);\n    }\n    return true;\n  });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'autoPlacement',\n    options,\n    async fn(state) {\n      var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n      const {\n        rects,\n        middlewareData,\n        placement,\n        platform,\n        elements\n      } = state;\n      const {\n        crossAxis = false,\n        alignment,\n        allowedPlacements = placements,\n        autoAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const placements$1 = alignment !== undefined || allowedPlacements === placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n      const currentPlacement = placements$1[currentIndex];\n      if (currentPlacement == null) {\n        return {};\n      }\n      const alignmentSides = getAlignmentSides(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n\n      // Make `computeCoords` start from the right place.\n      if (placement !== currentPlacement) {\n        return {\n          reset: {\n            placement: placements$1[0]\n          }\n        };\n      }\n      const currentOverflows = [overflow[getSide(currentPlacement)], overflow[alignmentSides[0]], overflow[alignmentSides[1]]];\n      const allOverflows = [...(((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || []), {\n        placement: currentPlacement,\n        overflows: currentOverflows\n      }];\n      const nextPlacement = placements$1[currentIndex + 1];\n\n      // There are more placements to check.\n      if (nextPlacement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: nextPlacement\n          }\n        };\n      }\n      const placementsSortedByMostSpace = allOverflows.map(d => {\n        const alignment = getAlignment(d.placement);\n        return [d.placement, alignment && crossAxis ?\n        // Check along the mainAxis and main crossAxis side.\n        d.overflows.slice(0, 2).reduce((acc, v) => acc + v, 0) :\n        // Check only the mainAxis.\n        d.overflows[0], d.overflows];\n      }).sort((a, b) => a[1] - b[1]);\n      const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter(d => d[2].slice(0,\n      // Aligned placements should not check their opposite crossAxis\n      // side.\n      getAlignment(d[0]) ? 2 : 3).every(v => v <= 0));\n      const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n      if (resetPlacement !== placement) {\n        return {\n          data: {\n            index: currentIndex + 1,\n            overflows: allOverflows\n          },\n          reset: {\n            placement: resetPlacement\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'flip',\n    options,\n    async fn(state) {\n      var _middlewareData$arrow, _middlewareData$flip;\n      const {\n        placement,\n        middlewareData,\n        rects,\n        initialPlacement,\n        platform,\n        elements\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true,\n        fallbackPlacements: specifiedFallbackPlacements,\n        fallbackStrategy = 'bestFit',\n        fallbackAxisSideDirection = 'none',\n        flipAlignment = true,\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n\n      // If a reset by the arrow was caused due to an alignment offset being\n      // added, we should skip any logic now since `flip()` has already done its\n      // work.\n      // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n      if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      const side = getSide(placement);\n      const initialSideAxis = getSideAxis(initialPlacement);\n      const isBasePlacement = getSide(initialPlacement) === initialPlacement;\n      const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n      const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [getOppositePlacement(initialPlacement)] : getExpandedPlacements(initialPlacement));\n      const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== 'none';\n      if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n        fallbackPlacements.push(...getOppositeAxisPlacements(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n      }\n      const placements = [initialPlacement, ...fallbackPlacements];\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const overflows = [];\n      let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n      if (checkMainAxis) {\n        overflows.push(overflow[side]);\n      }\n      if (checkCrossAxis) {\n        const sides = getAlignmentSides(placement, rects, rtl);\n        overflows.push(overflow[sides[0]], overflow[sides[1]]);\n      }\n      overflowsData = [...overflowsData, {\n        placement,\n        overflows\n      }];\n\n      // One or more sides is overflowing.\n      if (!overflows.every(side => side <= 0)) {\n        var _middlewareData$flip2, _overflowsData$filter;\n        const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n        const nextPlacement = placements[nextIndex];\n        if (nextPlacement) {\n          var _overflowsData$;\n          const ignoreCrossAxisOverflow = checkCrossAxis === 'alignment' ? initialSideAxis !== getSideAxis(nextPlacement) : false;\n          const hasInitialMainAxisOverflow = ((_overflowsData$ = overflowsData[0]) == null ? void 0 : _overflowsData$.overflows[0]) > 0;\n          if (!ignoreCrossAxisOverflow || hasInitialMainAxisOverflow) {\n            // Try next placement and re-run the lifecycle.\n            return {\n              data: {\n                index: nextIndex,\n                overflows: overflowsData\n              },\n              reset: {\n                placement: nextPlacement\n              }\n            };\n          }\n        }\n\n        // First, find the candidates that fit on the mainAxis side of overflow,\n        // then find the placement that fits the best on the main crossAxis side.\n        let resetPlacement = (_overflowsData$filter = overflowsData.filter(d => d.overflows[0] <= 0).sort((a, b) => a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n\n        // Otherwise fallback.\n        if (!resetPlacement) {\n          switch (fallbackStrategy) {\n            case 'bestFit':\n              {\n                var _overflowsData$filter2;\n                const placement = (_overflowsData$filter2 = overflowsData.filter(d => {\n                  if (hasFallbackAxisSideDirection) {\n                    const currentSideAxis = getSideAxis(d.placement);\n                    return currentSideAxis === initialSideAxis ||\n                    // Create a bias to the `y` side axis due to horizontal\n                    // reading directions favoring greater width.\n                    currentSideAxis === 'y';\n                  }\n                  return true;\n                }).map(d => [d.placement, d.overflows.filter(overflow => overflow > 0).reduce((acc, overflow) => acc + overflow, 0)]).sort((a, b) => a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                if (placement) {\n                  resetPlacement = placement;\n                }\n                break;\n              }\n            case 'initialPlacement':\n              resetPlacement = initialPlacement;\n              break;\n          }\n        }\n        if (placement !== resetPlacement) {\n          return {\n            reset: {\n              placement: resetPlacement\n            }\n          };\n        }\n      }\n      return {};\n    }\n  };\n};\n\nfunction getSideOffsets(overflow, rect) {\n  return {\n    top: overflow.top - rect.height,\n    right: overflow.right - rect.width,\n    bottom: overflow.bottom - rect.height,\n    left: overflow.left - rect.width\n  };\n}\nfunction isAnySideFullyClipped(overflow) {\n  return sides.some(side => overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'hide',\n    options,\n    async fn(state) {\n      const {\n        rects\n      } = state;\n      const {\n        strategy = 'referenceHidden',\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      switch (strategy) {\n        case 'referenceHidden':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              elementContext: 'reference'\n            });\n            const offsets = getSideOffsets(overflow, rects.reference);\n            return {\n              data: {\n                referenceHiddenOffsets: offsets,\n                referenceHidden: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        case 'escaped':\n          {\n            const overflow = await detectOverflow(state, {\n              ...detectOverflowOptions,\n              altBoundary: true\n            });\n            const offsets = getSideOffsets(overflow, rects.floating);\n            return {\n              data: {\n                escapedOffsets: offsets,\n                escaped: isAnySideFullyClipped(offsets)\n              }\n            };\n          }\n        default:\n          {\n            return {};\n          }\n      }\n    }\n  };\n};\n\nfunction getBoundingRect(rects) {\n  const minX = min(...rects.map(rect => rect.left));\n  const minY = min(...rects.map(rect => rect.top));\n  const maxX = max(...rects.map(rect => rect.right));\n  const maxY = max(...rects.map(rect => rect.bottom));\n  return {\n    x: minX,\n    y: minY,\n    width: maxX - minX,\n    height: maxY - minY\n  };\n}\nfunction getRectsByLine(rects) {\n  const sortedRects = rects.slice().sort((a, b) => a.y - b.y);\n  const groups = [];\n  let prevRect = null;\n  for (let i = 0; i < sortedRects.length; i++) {\n    const rect = sortedRects[i];\n    if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n      groups.push([rect]);\n    } else {\n      groups[groups.length - 1].push(rect);\n    }\n    prevRect = rect;\n  }\n  return groups.map(rect => rectToClientRect(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'inline',\n    options,\n    async fn(state) {\n      const {\n        placement,\n        elements,\n        rects,\n        platform,\n        strategy\n      } = state;\n      // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n      // ClientRect's bounds, despite the event listener being triggered. A\n      // padding of 2 seems to handle this issue.\n      const {\n        padding = 2,\n        x,\n        y\n      } = evaluate(options, state);\n      const nativeClientRects = Array.from((await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference))) || []);\n      const clientRects = getRectsByLine(nativeClientRects);\n      const fallback = rectToClientRect(getBoundingRect(nativeClientRects));\n      const paddingObject = getPaddingObject(padding);\n      function getBoundingClientRect() {\n        // There are two rects and they are disjoined.\n        if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n          // Find the first rect in which the point is fully inside.\n          return clientRects.find(rect => x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n        }\n\n        // There are 2 or more connected rects.\n        if (clientRects.length >= 2) {\n          if (getSideAxis(placement) === 'y') {\n            const firstRect = clientRects[0];\n            const lastRect = clientRects[clientRects.length - 1];\n            const isTop = getSide(placement) === 'top';\n            const top = firstRect.top;\n            const bottom = lastRect.bottom;\n            const left = isTop ? firstRect.left : lastRect.left;\n            const right = isTop ? firstRect.right : lastRect.right;\n            const width = right - left;\n            const height = bottom - top;\n            return {\n              top,\n              bottom,\n              left,\n              right,\n              width,\n              height,\n              x: left,\n              y: top\n            };\n          }\n          const isLeftSide = getSide(placement) === 'left';\n          const maxRight = max(...clientRects.map(rect => rect.right));\n          const minLeft = min(...clientRects.map(rect => rect.left));\n          const measureRects = clientRects.filter(rect => isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n          const top = measureRects[0].top;\n          const bottom = measureRects[measureRects.length - 1].bottom;\n          const left = minLeft;\n          const right = maxRight;\n          const width = right - left;\n          const height = bottom - top;\n          return {\n            top,\n            bottom,\n            left,\n            right,\n            width,\n            height,\n            x: left,\n            y: top\n          };\n        }\n        return fallback;\n      }\n      const resetRects = await platform.getElementRects({\n        reference: {\n          getBoundingClientRect\n        },\n        floating: elements.floating,\n        strategy\n      });\n      if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n        return {\n          reset: {\n            rects: resetRects\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\n\nasync function convertValueToCoords(state, options) {\n  const {\n    placement,\n    platform,\n    elements\n  } = state;\n  const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n  const side = getSide(placement);\n  const alignment = getAlignment(placement);\n  const isVertical = getSideAxis(placement) === 'y';\n  const mainAxisMulti = ['left', 'top'].includes(side) ? -1 : 1;\n  const crossAxisMulti = rtl && isVertical ? -1 : 1;\n  const rawValue = evaluate(options, state);\n\n  // eslint-disable-next-line prefer-const\n  let {\n    mainAxis,\n    crossAxis,\n    alignmentAxis\n  } = typeof rawValue === 'number' ? {\n    mainAxis: rawValue,\n    crossAxis: 0,\n    alignmentAxis: null\n  } : {\n    mainAxis: rawValue.mainAxis || 0,\n    crossAxis: rawValue.crossAxis || 0,\n    alignmentAxis: rawValue.alignmentAxis\n  };\n  if (alignment && typeof alignmentAxis === 'number') {\n    crossAxis = alignment === 'end' ? alignmentAxis * -1 : alignmentAxis;\n  }\n  return isVertical ? {\n    x: crossAxis * crossAxisMulti,\n    y: mainAxis * mainAxisMulti\n  } : {\n    x: mainAxis * mainAxisMulti,\n    y: crossAxis * crossAxisMulti\n  };\n}\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = function (options) {\n  if (options === void 0) {\n    options = 0;\n  }\n  return {\n    name: 'offset',\n    options,\n    async fn(state) {\n      var _middlewareData$offse, _middlewareData$arrow;\n      const {\n        x,\n        y,\n        placement,\n        middlewareData\n      } = state;\n      const diffCoords = await convertValueToCoords(state, options);\n\n      // If the placement is the same and the arrow caused an alignment offset\n      // then we don't need to change the positioning coordinates.\n      if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n        return {};\n      }\n      return {\n        x: x + diffCoords.x,\n        y: y + diffCoords.y,\n        data: {\n          ...diffCoords,\n          placement\n        }\n      };\n    }\n  };\n};\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'shift',\n    options,\n    async fn(state) {\n      const {\n        x,\n        y,\n        placement\n      } = state;\n      const {\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = false,\n        limiter = {\n          fn: _ref => {\n            let {\n              x,\n              y\n            } = _ref;\n            return {\n              x,\n              y\n            };\n          }\n        },\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const crossAxis = getSideAxis(getSide(placement));\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      if (checkMainAxis) {\n        const minSide = mainAxis === 'y' ? 'top' : 'left';\n        const maxSide = mainAxis === 'y' ? 'bottom' : 'right';\n        const min = mainAxisCoord + overflow[minSide];\n        const max = mainAxisCoord - overflow[maxSide];\n        mainAxisCoord = clamp(min, mainAxisCoord, max);\n      }\n      if (checkCrossAxis) {\n        const minSide = crossAxis === 'y' ? 'top' : 'left';\n        const maxSide = crossAxis === 'y' ? 'bottom' : 'right';\n        const min = crossAxisCoord + overflow[minSide];\n        const max = crossAxisCoord - overflow[maxSide];\n        crossAxisCoord = clamp(min, crossAxisCoord, max);\n      }\n      const limitedCoords = limiter.fn({\n        ...state,\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      });\n      return {\n        ...limitedCoords,\n        data: {\n          x: limitedCoords.x - x,\n          y: limitedCoords.y - y,\n          enabled: {\n            [mainAxis]: checkMainAxis,\n            [crossAxis]: checkCrossAxis\n          }\n        }\n      };\n    }\n  };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    options,\n    fn(state) {\n      const {\n        x,\n        y,\n        placement,\n        rects,\n        middlewareData\n      } = state;\n      const {\n        offset = 0,\n        mainAxis: checkMainAxis = true,\n        crossAxis: checkCrossAxis = true\n      } = evaluate(options, state);\n      const coords = {\n        x,\n        y\n      };\n      const crossAxis = getSideAxis(placement);\n      const mainAxis = getOppositeAxis(crossAxis);\n      let mainAxisCoord = coords[mainAxis];\n      let crossAxisCoord = coords[crossAxis];\n      const rawOffset = evaluate(offset, state);\n      const computedOffset = typeof rawOffset === 'number' ? {\n        mainAxis: rawOffset,\n        crossAxis: 0\n      } : {\n        mainAxis: 0,\n        crossAxis: 0,\n        ...rawOffset\n      };\n      if (checkMainAxis) {\n        const len = mainAxis === 'y' ? 'height' : 'width';\n        const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n        const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n        if (mainAxisCoord < limitMin) {\n          mainAxisCoord = limitMin;\n        } else if (mainAxisCoord > limitMax) {\n          mainAxisCoord = limitMax;\n        }\n      }\n      if (checkCrossAxis) {\n        var _middlewareData$offse, _middlewareData$offse2;\n        const len = mainAxis === 'y' ? 'width' : 'height';\n        const isOriginSide = ['top', 'left'].includes(getSide(placement));\n        const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n        const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n        if (crossAxisCoord < limitMin) {\n          crossAxisCoord = limitMin;\n        } else if (crossAxisCoord > limitMax) {\n          crossAxisCoord = limitMax;\n        }\n      }\n      return {\n        [mainAxis]: mainAxisCoord,\n        [crossAxis]: crossAxisCoord\n      };\n    }\n  };\n};\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  return {\n    name: 'size',\n    options,\n    async fn(state) {\n      var _state$middlewareData, _state$middlewareData2;\n      const {\n        placement,\n        rects,\n        platform,\n        elements\n      } = state;\n      const {\n        apply = () => {},\n        ...detectOverflowOptions\n      } = evaluate(options, state);\n      const overflow = await detectOverflow(state, detectOverflowOptions);\n      const side = getSide(placement);\n      const alignment = getAlignment(placement);\n      const isYAxis = getSideAxis(placement) === 'y';\n      const {\n        width,\n        height\n      } = rects.floating;\n      let heightSide;\n      let widthSide;\n      if (side === 'top' || side === 'bottom') {\n        heightSide = side;\n        widthSide = alignment === ((await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating))) ? 'start' : 'end') ? 'left' : 'right';\n      } else {\n        widthSide = side;\n        heightSide = alignment === 'end' ? 'top' : 'bottom';\n      }\n      const maximumClippingHeight = height - overflow.top - overflow.bottom;\n      const maximumClippingWidth = width - overflow.left - overflow.right;\n      const overflowAvailableHeight = min(height - overflow[heightSide], maximumClippingHeight);\n      const overflowAvailableWidth = min(width - overflow[widthSide], maximumClippingWidth);\n      const noShift = !state.middlewareData.shift;\n      let availableHeight = overflowAvailableHeight;\n      let availableWidth = overflowAvailableWidth;\n      if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n        availableWidth = maximumClippingWidth;\n      }\n      if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n        availableHeight = maximumClippingHeight;\n      }\n      if (noShift && !alignment) {\n        const xMin = max(overflow.left, 0);\n        const xMax = max(overflow.right, 0);\n        const yMin = max(overflow.top, 0);\n        const yMax = max(overflow.bottom, 0);\n        if (isYAxis) {\n          availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : max(overflow.left, overflow.right));\n        } else {\n          availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : max(overflow.top, overflow.bottom));\n        }\n      }\n      await apply({\n        ...state,\n        availableWidth,\n        availableHeight\n      });\n      const nextDimensions = await platform.getDimensions(elements.floating);\n      if (width !== nextDimensions.width || height !== nextDimensions.height) {\n        return {\n          reset: {\n            rects: true\n          }\n        };\n      }\n      return {};\n    }\n  };\n};\n\nexport { arrow, autoPlacement, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, shift, size };\n", "function hasWindow() {\n  return typeof window !== 'undefined';\n}\nfunction getNodeName(node) {\n  if (isNode(node)) {\n    return (node.nodeName || '').toLowerCase();\n  }\n  // Mocked nodes in testing environments may not be instances of Node. By\n  // returning `#document` an infinite loop won't occur.\n  // https://github.com/floating-ui/floating-ui/issues/2317\n  return '#document';\n}\nfunction getWindow(node) {\n  var _node$ownerDocument;\n  return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n  var _ref;\n  return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n  if (!hasWindow()) {\n    return false;\n  }\n  return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n  if (!hasWindow() || typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n  return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n  const {\n    overflow,\n    overflowX,\n    overflowY,\n    display\n  } = getComputedStyle(element);\n  return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && !['inline', 'contents'].includes(display);\n}\nfunction isTableElement(element) {\n  return ['table', 'td', 'th'].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n  return [':popover-open', ':modal'].some(selector => {\n    try {\n      return element.matches(selector);\n    } catch (e) {\n      return false;\n    }\n  });\n}\nfunction isContainingBlock(elementOrCss) {\n  const webkit = isWebKit();\n  const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n  return ['transform', 'translate', 'scale', 'rotate', 'perspective'].some(value => css[value] ? css[value] !== 'none' : false) || (css.containerType ? css.containerType !== 'normal' : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== 'none' : false) || !webkit && (css.filter ? css.filter !== 'none' : false) || ['transform', 'translate', 'scale', 'rotate', 'perspective', 'filter'].some(value => (css.willChange || '').includes(value)) || ['paint', 'layout', 'strict', 'content'].some(value => (css.contain || '').includes(value));\n}\nfunction getContainingBlock(element) {\n  let currentNode = getParentNode(element);\n  while (isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    if (isContainingBlock(currentNode)) {\n      return currentNode;\n    } else if (isTopLayer(currentNode)) {\n      return null;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  return null;\n}\nfunction isWebKit() {\n  if (typeof CSS === 'undefined' || !CSS.supports) return false;\n  return CSS.supports('-webkit-backdrop-filter', 'none');\n}\nfunction isLastTraversableNode(node) {\n  return ['html', 'body', '#document'].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n  if (isElement(element)) {\n    return {\n      scrollLeft: element.scrollLeft,\n      scrollTop: element.scrollTop\n    };\n  }\n  return {\n    scrollLeft: element.scrollX,\n    scrollTop: element.scrollY\n  };\n}\nfunction getParentNode(node) {\n  if (getNodeName(node) === 'html') {\n    return node;\n  }\n  const result =\n  // Step into the shadow DOM of the parent of a slotted node.\n  node.assignedSlot ||\n  // DOM Element detected.\n  node.parentNode ||\n  // ShadowRoot detected.\n  isShadowRoot(node) && node.host ||\n  // Fallback.\n  getDocumentElement(node);\n  return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n  const parentNode = getParentNode(node);\n  if (isLastTraversableNode(parentNode)) {\n    return node.ownerDocument ? node.ownerDocument.body : node.body;\n  }\n  if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n    return parentNode;\n  }\n  return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n  var _node$ownerDocument2;\n  if (list === void 0) {\n    list = [];\n  }\n  if (traverseIframes === void 0) {\n    traverseIframes = true;\n  }\n  const scrollableAncestor = getNearestOverflowAncestor(node);\n  const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n  const win = getWindow(scrollableAncestor);\n  if (isBody) {\n    const frameElement = getFrameElement(win);\n    return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n  }\n  return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n  return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\nexport { getComputedStyle, getContainingBlock, getDocumentElement, getFrameElement, getNearestOverflowAncestor, getNodeName, getNodeScroll, getOverflowAncestors, getParentNode, getWindow, isContainingBlock, isElement, isHTMLElement, isLastTraversableNode, isNode, isOverflowElement, isShadowRoot, isTableElement, isTopLayer, isWebKit };\n", "import { rectToClientRect, arrow as arrow$1, autoPlacement as autoPlacement$1, detectOverflow as detectOverflow$1, flip as flip$1, hide as hide$1, inline as inline$1, limitShift as limitShift$1, offset as offset$1, shift as shift$1, size as size$1, computePosition as computePosition$1 } from '@floating-ui/core';\nimport { round, createCoords, max, min, floor } from '@floating-ui/utils';\nimport { getComputedStyle, isHTMLElement, isElement, getWindow, isWebKit, getFrameElement, getNodeScroll, getDocumentElement, isTopLayer, getNodeName, isOverflowElement, getOverflowAncestors, getParentNode, isLastTraversableNode, isContainingBlock, isTableElement, getContainingBlock } from '@floating-ui/utils/dom';\nexport { getOverflowAncestors } from '@floating-ui/utils/dom';\n\nfunction getCssDimensions(element) {\n  const css = getComputedStyle(element);\n  // In testing environments, the `width` and `height` properties are empty\n  // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n  let width = parseFloat(css.width) || 0;\n  let height = parseFloat(css.height) || 0;\n  const hasOffset = isHTMLElement(element);\n  const offsetWidth = hasOffset ? element.offsetWidth : width;\n  const offsetHeight = hasOffset ? element.offsetHeight : height;\n  const shouldFallback = round(width) !== offsetWidth || round(height) !== offsetHeight;\n  if (shouldFallback) {\n    width = offsetWidth;\n    height = offsetHeight;\n  }\n  return {\n    width,\n    height,\n    $: shouldFallback\n  };\n}\n\nfunction unwrapElement(element) {\n  return !isElement(element) ? element.contextElement : element;\n}\n\nfunction getScale(element) {\n  const domElement = unwrapElement(element);\n  if (!isHTMLElement(domElement)) {\n    return createCoords(1);\n  }\n  const rect = domElement.getBoundingClientRect();\n  const {\n    width,\n    height,\n    $\n  } = getCssDimensions(domElement);\n  let x = ($ ? round(rect.width) : rect.width) / width;\n  let y = ($ ? round(rect.height) : rect.height) / height;\n\n  // 0, NaN, or Infinity should always fallback to 1.\n\n  if (!x || !Number.isFinite(x)) {\n    x = 1;\n  }\n  if (!y || !Number.isFinite(y)) {\n    y = 1;\n  }\n  return {\n    x,\n    y\n  };\n}\n\nconst noOffsets = /*#__PURE__*/createCoords(0);\nfunction getVisualOffsets(element) {\n  const win = getWindow(element);\n  if (!isWebKit() || !win.visualViewport) {\n    return noOffsets;\n  }\n  return {\n    x: win.visualViewport.offsetLeft,\n    y: win.visualViewport.offsetTop\n  };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n  if (!floatingOffsetParent || isFixed && floatingOffsetParent !== getWindow(element)) {\n    return false;\n  }\n  return isFixed;\n}\n\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n  const clientRect = element.getBoundingClientRect();\n  const domElement = unwrapElement(element);\n  let scale = createCoords(1);\n  if (includeScale) {\n    if (offsetParent) {\n      if (isElement(offsetParent)) {\n        scale = getScale(offsetParent);\n      }\n    } else {\n      scale = getScale(element);\n    }\n  }\n  const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : createCoords(0);\n  let x = (clientRect.left + visualOffsets.x) / scale.x;\n  let y = (clientRect.top + visualOffsets.y) / scale.y;\n  let width = clientRect.width / scale.x;\n  let height = clientRect.height / scale.y;\n  if (domElement) {\n    const win = getWindow(domElement);\n    const offsetWin = offsetParent && isElement(offsetParent) ? getWindow(offsetParent) : offsetParent;\n    let currentWin = win;\n    let currentIFrame = getFrameElement(currentWin);\n    while (currentIFrame && offsetParent && offsetWin !== currentWin) {\n      const iframeScale = getScale(currentIFrame);\n      const iframeRect = currentIFrame.getBoundingClientRect();\n      const css = getComputedStyle(currentIFrame);\n      const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n      const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n      x *= iframeScale.x;\n      y *= iframeScale.y;\n      width *= iframeScale.x;\n      height *= iframeScale.y;\n      x += left;\n      y += top;\n      currentWin = getWindow(currentIFrame);\n      currentIFrame = getFrameElement(currentWin);\n    }\n  }\n  return rectToClientRect({\n    width,\n    height,\n    x,\n    y\n  });\n}\n\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n  const leftScroll = getNodeScroll(element).scrollLeft;\n  if (!rect) {\n    return getBoundingClientRect(getDocumentElement(element)).left + leftScroll;\n  }\n  return rect.left + leftScroll;\n}\n\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n  if (ignoreScrollbarX === void 0) {\n    ignoreScrollbarX = false;\n  }\n  const htmlRect = documentElement.getBoundingClientRect();\n  const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 :\n  // RTL <body> scrollbar.\n  getWindowScrollBarX(documentElement, htmlRect));\n  const y = htmlRect.top + scroll.scrollTop;\n  return {\n    x,\n    y\n  };\n}\n\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n  let {\n    elements,\n    rect,\n    offsetParent,\n    strategy\n  } = _ref;\n  const isFixed = strategy === 'fixed';\n  const documentElement = getDocumentElement(offsetParent);\n  const topLayer = elements ? isTopLayer(elements.floating) : false;\n  if (offsetParent === documentElement || topLayer && isFixed) {\n    return rect;\n  }\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  let scale = createCoords(1);\n  const offsets = createCoords(0);\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isHTMLElement(offsetParent)) {\n      const offsetRect = getBoundingClientRect(offsetParent);\n      scale = getScale(offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    }\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : createCoords(0);\n  return {\n    width: rect.width * scale.x,\n    height: rect.height * scale.y,\n    x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n    y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n  };\n}\n\nfunction getClientRects(element) {\n  return Array.from(element.getClientRects());\n}\n\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n  const html = getDocumentElement(element);\n  const scroll = getNodeScroll(element);\n  const body = element.ownerDocument.body;\n  const width = max(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n  const height = max(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n  let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n  const y = -scroll.scrollTop;\n  if (getComputedStyle(body).direction === 'rtl') {\n    x += max(html.clientWidth, body.clientWidth) - width;\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\nfunction getViewportRect(element, strategy) {\n  const win = getWindow(element);\n  const html = getDocumentElement(element);\n  const visualViewport = win.visualViewport;\n  let width = html.clientWidth;\n  let height = html.clientHeight;\n  let x = 0;\n  let y = 0;\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    const visualViewportBased = isWebKit();\n    if (!visualViewportBased || visualViewportBased && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\n\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n  const clientRect = getBoundingClientRect(element, true, strategy === 'fixed');\n  const top = clientRect.top + element.clientTop;\n  const left = clientRect.left + element.clientLeft;\n  const scale = isHTMLElement(element) ? getScale(element) : createCoords(1);\n  const width = element.clientWidth * scale.x;\n  const height = element.clientHeight * scale.y;\n  const x = left * scale.x;\n  const y = top * scale.y;\n  return {\n    width,\n    height,\n    x,\n    y\n  };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n  let rect;\n  if (clippingAncestor === 'viewport') {\n    rect = getViewportRect(element, strategy);\n  } else if (clippingAncestor === 'document') {\n    rect = getDocumentRect(getDocumentElement(element));\n  } else if (isElement(clippingAncestor)) {\n    rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n  } else {\n    const visualOffsets = getVisualOffsets(element);\n    rect = {\n      x: clippingAncestor.x - visualOffsets.x,\n      y: clippingAncestor.y - visualOffsets.y,\n      width: clippingAncestor.width,\n      height: clippingAncestor.height\n    };\n  }\n  return rectToClientRect(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n  const parentNode = getParentNode(element);\n  if (parentNode === stopNode || !isElement(parentNode) || isLastTraversableNode(parentNode)) {\n    return false;\n  }\n  return getComputedStyle(parentNode).position === 'fixed' || hasFixedPositionAncestor(parentNode, stopNode);\n}\n\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n  const cachedResult = cache.get(element);\n  if (cachedResult) {\n    return cachedResult;\n  }\n  let result = getOverflowAncestors(element, [], false).filter(el => isElement(el) && getNodeName(el) !== 'body');\n  let currentContainingBlockComputedStyle = null;\n  const elementIsFixed = getComputedStyle(element).position === 'fixed';\n  let currentNode = elementIsFixed ? getParentNode(element) : element;\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n  while (isElement(currentNode) && !isLastTraversableNode(currentNode)) {\n    const computedStyle = getComputedStyle(currentNode);\n    const currentNodeIsContaining = isContainingBlock(currentNode);\n    if (!currentNodeIsContaining && computedStyle.position === 'fixed') {\n      currentContainingBlockComputedStyle = null;\n    }\n    const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === 'static' && !!currentContainingBlockComputedStyle && ['absolute', 'fixed'].includes(currentContainingBlockComputedStyle.position) || isOverflowElement(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n    if (shouldDropCurrentNode) {\n      // Drop non-containing blocks.\n      result = result.filter(ancestor => ancestor !== currentNode);\n    } else {\n      // Record last containing block for next iteration.\n      currentContainingBlockComputedStyle = computedStyle;\n    }\n    currentNode = getParentNode(currentNode);\n  }\n  cache.set(element, result);\n  return result;\n}\n\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n  let {\n    element,\n    boundary,\n    rootBoundary,\n    strategy\n  } = _ref;\n  const elementClippingAncestors = boundary === 'clippingAncestors' ? isTopLayer(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n  const clippingAncestors = [...elementClippingAncestors, rootBoundary];\n  const firstClippingAncestor = clippingAncestors[0];\n  const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor) => {\n    const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n  return {\n    width: clippingRect.right - clippingRect.left,\n    height: clippingRect.bottom - clippingRect.top,\n    x: clippingRect.left,\n    y: clippingRect.top\n  };\n}\n\nfunction getDimensions(element) {\n  const {\n    width,\n    height\n  } = getCssDimensions(element);\n  return {\n    width,\n    height\n  };\n}\n\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n  const isOffsetParentAnElement = isHTMLElement(offsetParent);\n  const documentElement = getDocumentElement(offsetParent);\n  const isFixed = strategy === 'fixed';\n  const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n  let scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  const offsets = createCoords(0);\n\n  // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n  // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n  function setLeftRTLScrollbarOffset() {\n    offsets.x = getWindowScrollBarX(documentElement);\n  }\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || isOverflowElement(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n    if (isOffsetParentAnElement) {\n      const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n      offsets.x = offsetRect.x + offsetParent.clientLeft;\n      offsets.y = offsetRect.y + offsetParent.clientTop;\n    } else if (documentElement) {\n      setLeftRTLScrollbarOffset();\n    }\n  }\n  if (isFixed && !isOffsetParentAnElement && documentElement) {\n    setLeftRTLScrollbarOffset();\n  }\n  const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : createCoords(0);\n  const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n  const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n  return {\n    x,\n    y,\n    width: rect.width,\n    height: rect.height\n  };\n}\n\nfunction isStaticPositioned(element) {\n  return getComputedStyle(element).position === 'static';\n}\n\nfunction getTrueOffsetParent(element, polyfill) {\n  if (!isHTMLElement(element) || getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n  if (polyfill) {\n    return polyfill(element);\n  }\n  let rawOffsetParent = element.offsetParent;\n\n  // Firefox returns the <html> element as the offsetParent if it's non-static,\n  // while Chrome and Safari return the <body> element. The <body> element must\n  // be used to perform the correct calculations even if the <html> element is\n  // non-static.\n  if (getDocumentElement(element) === rawOffsetParent) {\n    rawOffsetParent = rawOffsetParent.ownerDocument.body;\n  }\n  return rawOffsetParent;\n}\n\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n  const win = getWindow(element);\n  if (isTopLayer(element)) {\n    return win;\n  }\n  if (!isHTMLElement(element)) {\n    let svgOffsetParent = getParentNode(element);\n    while (svgOffsetParent && !isLastTraversableNode(svgOffsetParent)) {\n      if (isElement(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n        return svgOffsetParent;\n      }\n      svgOffsetParent = getParentNode(svgOffsetParent);\n    }\n    return win;\n  }\n  let offsetParent = getTrueOffsetParent(element, polyfill);\n  while (offsetParent && isTableElement(offsetParent) && isStaticPositioned(offsetParent)) {\n    offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n  }\n  if (offsetParent && isLastTraversableNode(offsetParent) && isStaticPositioned(offsetParent) && !isContainingBlock(offsetParent)) {\n    return win;\n  }\n  return offsetParent || getContainingBlock(element) || win;\n}\n\nconst getElementRects = async function (data) {\n  const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n  const getDimensionsFn = this.getDimensions;\n  const floatingDimensions = await getDimensionsFn(data.floating);\n  return {\n    reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n    floating: {\n      x: 0,\n      y: 0,\n      width: floatingDimensions.width,\n      height: floatingDimensions.height\n    }\n  };\n};\n\nfunction isRTL(element) {\n  return getComputedStyle(element).direction === 'rtl';\n}\n\nconst platform = {\n  convertOffsetParentRelativeRectToViewportRelativeRect,\n  getDocumentElement,\n  getClippingRect,\n  getOffsetParent,\n  getElementRects,\n  getClientRects,\n  getDimensions,\n  getScale,\n  isElement,\n  isRTL\n};\n\nfunction rectsAreEqual(a, b) {\n  return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n  let io = null;\n  let timeoutId;\n  const root = getDocumentElement(element);\n  function cleanup() {\n    var _io;\n    clearTimeout(timeoutId);\n    (_io = io) == null || _io.disconnect();\n    io = null;\n  }\n  function refresh(skip, threshold) {\n    if (skip === void 0) {\n      skip = false;\n    }\n    if (threshold === void 0) {\n      threshold = 1;\n    }\n    cleanup();\n    const elementRectForRootMargin = element.getBoundingClientRect();\n    const {\n      left,\n      top,\n      width,\n      height\n    } = elementRectForRootMargin;\n    if (!skip) {\n      onMove();\n    }\n    if (!width || !height) {\n      return;\n    }\n    const insetTop = floor(top);\n    const insetRight = floor(root.clientWidth - (left + width));\n    const insetBottom = floor(root.clientHeight - (top + height));\n    const insetLeft = floor(left);\n    const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n    const options = {\n      rootMargin,\n      threshold: max(0, min(1, threshold)) || 1\n    };\n    let isFirstUpdate = true;\n    function handleObserve(entries) {\n      const ratio = entries[0].intersectionRatio;\n      if (ratio !== threshold) {\n        if (!isFirstUpdate) {\n          return refresh();\n        }\n        if (!ratio) {\n          // If the reference is clipped, the ratio is 0. Throttle the refresh\n          // to prevent an infinite loop of updates.\n          timeoutId = setTimeout(() => {\n            refresh(false, 1e-7);\n          }, 1000);\n        } else {\n          refresh(false, ratio);\n        }\n      }\n      if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n        // It's possible that even though the ratio is reported as 1, the\n        // element is not actually fully within the IntersectionObserver's root\n        // area anymore. This can happen under performance constraints. This may\n        // be a bug in the browser's IntersectionObserver implementation. To\n        // work around this, we compare the element's bounding rect now with\n        // what it was at the time we created the IntersectionObserver. If they\n        // are not equal then the element moved, so we refresh.\n        refresh();\n      }\n      isFirstUpdate = false;\n    }\n\n    // Older browsers don't support a `document` as the root and will throw an\n    // error.\n    try {\n      io = new IntersectionObserver(handleObserve, {\n        ...options,\n        // Handle <iframe>s\n        root: root.ownerDocument\n      });\n    } catch (_e) {\n      io = new IntersectionObserver(handleObserve, options);\n    }\n    io.observe(element);\n  }\n  refresh(true);\n  return cleanup;\n}\n\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */\nfunction autoUpdate(reference, floating, update, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    ancestorScroll = true,\n    ancestorResize = true,\n    elementResize = typeof ResizeObserver === 'function',\n    layoutShift = typeof IntersectionObserver === 'function',\n    animationFrame = false\n  } = options;\n  const referenceEl = unwrapElement(reference);\n  const ancestors = ancestorScroll || ancestorResize ? [...(referenceEl ? getOverflowAncestors(referenceEl) : []), ...getOverflowAncestors(floating)] : [];\n  ancestors.forEach(ancestor => {\n    ancestorScroll && ancestor.addEventListener('scroll', update, {\n      passive: true\n    });\n    ancestorResize && ancestor.addEventListener('resize', update);\n  });\n  const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n  let reobserveFrame = -1;\n  let resizeObserver = null;\n  if (elementResize) {\n    resizeObserver = new ResizeObserver(_ref => {\n      let [firstEntry] = _ref;\n      if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n        // Prevent update loops when using the `size` middleware.\n        // https://github.com/floating-ui/floating-ui/issues/1740\n        resizeObserver.unobserve(floating);\n        cancelAnimationFrame(reobserveFrame);\n        reobserveFrame = requestAnimationFrame(() => {\n          var _resizeObserver;\n          (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n        });\n      }\n      update();\n    });\n    if (referenceEl && !animationFrame) {\n      resizeObserver.observe(referenceEl);\n    }\n    resizeObserver.observe(floating);\n  }\n  let frameId;\n  let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n  if (animationFrame) {\n    frameLoop();\n  }\n  function frameLoop() {\n    const nextRefRect = getBoundingClientRect(reference);\n    if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n      update();\n    }\n    prevRefRect = nextRefRect;\n    frameId = requestAnimationFrame(frameLoop);\n  }\n  update();\n  return () => {\n    var _resizeObserver2;\n    ancestors.forEach(ancestor => {\n      ancestorScroll && ancestor.removeEventListener('scroll', update);\n      ancestorResize && ancestor.removeEventListener('resize', update);\n    });\n    cleanupIo == null || cleanupIo();\n    (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n    resizeObserver = null;\n    if (animationFrame) {\n      cancelAnimationFrame(frameId);\n    }\n  };\n}\n\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */\nconst detectOverflow = detectOverflow$1;\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = offset$1;\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = autoPlacement$1;\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = shift$1;\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = flip$1;\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = size$1;\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = hide$1;\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = arrow$1;\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = inline$1;\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = limitShift$1;\n\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */\nconst computePosition = (reference, floating, options) => {\n  // This caches the expensive `getClippingElementAncestors` function so that\n  // multiple lifecycle resets re-use the same result. It only lives for a\n  // single call. If other functions become expensive, we can add them as well.\n  const cache = new Map();\n  const mergedOptions = {\n    platform,\n    ...options\n  };\n  const platformWithCache = {\n    ...mergedOptions.platform,\n    _c: cache\n  };\n  return computePosition$1(reference, floating, {\n    ...mergedOptions,\n    platform: platformWithCache\n  });\n};\n\nexport { arrow, autoPlacement, autoUpdate, computePosition, detectOverflow, flip, hide, inline, limitShift, offset, platform, shift, size };\n", "import { computePosition, arrow as arrow$2, offset as offset$1, shift as shift$1, limitShift as limitShift$1, flip as flip$1, size as size$1, autoPlacement as autoPlacement$1, hide as hide$1, inline as inline$1 } from '@floating-ui/dom';\nexport { autoUpdate, computePosition, detectOverflow, getOverflowAncestors, platform } from '@floating-ui/dom';\nimport * as React from 'react';\nimport { useLayoutEffect, useEffect } from 'react';\nimport * as ReactDOM from 'react-dom';\n\nvar index = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\n// Fork of `fast-deep-equal` that only does the comparisons we need and compares\n// functions\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (typeof a === 'function' && a.toString() === b.toString()) {\n    return true;\n  }\n  let length;\n  let i;\n  let keys;\n  if (a && b && typeof a === 'object') {\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length !== b.length) return false;\n      for (i = length; i-- !== 0;) {\n        if (!deepEqual(a[i], b[i])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) {\n      return false;\n    }\n    for (i = length; i-- !== 0;) {\n      if (!{}.hasOwnProperty.call(b, keys[i])) {\n        return false;\n      }\n    }\n    for (i = length; i-- !== 0;) {\n      const key = keys[i];\n      if (key === '_owner' && a.$$typeof) {\n        continue;\n      }\n      if (!deepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return a !== a && b !== b;\n}\n\nfunction getDPR(element) {\n  if (typeof window === 'undefined') {\n    return 1;\n  }\n  const win = element.ownerDocument.defaultView || window;\n  return win.devicePixelRatio || 1;\n}\n\nfunction roundByDPR(element, value) {\n  const dpr = getDPR(element);\n  return Math.round(value * dpr) / dpr;\n}\n\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\n/**\n * Provides data to position a floating element.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform,\n    elements: {\n      reference: externalReference,\n      floating: externalFloating\n    } = {},\n    transform = true,\n    whileElementsMounted,\n    open\n  } = options;\n  const [data, setData] = React.useState({\n    x: 0,\n    y: 0,\n    strategy,\n    placement,\n    middlewareData: {},\n    isPositioned: false\n  });\n  const [latestMiddleware, setLatestMiddleware] = React.useState(middleware);\n  if (!deepEqual(latestMiddleware, middleware)) {\n    setLatestMiddleware(middleware);\n  }\n  const [_reference, _setReference] = React.useState(null);\n  const [_floating, _setFloating] = React.useState(null);\n  const setReference = React.useCallback(node => {\n    if (node !== referenceRef.current) {\n      referenceRef.current = node;\n      _setReference(node);\n    }\n  }, []);\n  const setFloating = React.useCallback(node => {\n    if (node !== floatingRef.current) {\n      floatingRef.current = node;\n      _setFloating(node);\n    }\n  }, []);\n  const referenceEl = externalReference || _reference;\n  const floatingEl = externalFloating || _floating;\n  const referenceRef = React.useRef(null);\n  const floatingRef = React.useRef(null);\n  const dataRef = React.useRef(data);\n  const hasWhileElementsMounted = whileElementsMounted != null;\n  const whileElementsMountedRef = useLatestRef(whileElementsMounted);\n  const platformRef = useLatestRef(platform);\n  const openRef = useLatestRef(open);\n  const update = React.useCallback(() => {\n    if (!referenceRef.current || !floatingRef.current) {\n      return;\n    }\n    const config = {\n      placement,\n      strategy,\n      middleware: latestMiddleware\n    };\n    if (platformRef.current) {\n      config.platform = platformRef.current;\n    }\n    computePosition(referenceRef.current, floatingRef.current, config).then(data => {\n      const fullData = {\n        ...data,\n        // The floating element's position may be recomputed while it's closed\n        // but still mounted (such as when transitioning out). To ensure\n        // `isPositioned` will be `false` initially on the next open, avoid\n        // setting it to `true` when `open === false` (must be specified).\n        isPositioned: openRef.current !== false\n      };\n      if (isMountedRef.current && !deepEqual(dataRef.current, fullData)) {\n        dataRef.current = fullData;\n        ReactDOM.flushSync(() => {\n          setData(fullData);\n        });\n      }\n    });\n  }, [latestMiddleware, placement, strategy, platformRef, openRef]);\n  index(() => {\n    if (open === false && dataRef.current.isPositioned) {\n      dataRef.current.isPositioned = false;\n      setData(data => ({\n        ...data,\n        isPositioned: false\n      }));\n    }\n  }, [open]);\n  const isMountedRef = React.useRef(false);\n  index(() => {\n    isMountedRef.current = true;\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  index(() => {\n    if (referenceEl) referenceRef.current = referenceEl;\n    if (floatingEl) floatingRef.current = floatingEl;\n    if (referenceEl && floatingEl) {\n      if (whileElementsMountedRef.current) {\n        return whileElementsMountedRef.current(referenceEl, floatingEl, update);\n      }\n      update();\n    }\n  }, [referenceEl, floatingEl, update, whileElementsMountedRef, hasWhileElementsMounted]);\n  const refs = React.useMemo(() => ({\n    reference: referenceRef,\n    floating: floatingRef,\n    setReference,\n    setFloating\n  }), [setReference, setFloating]);\n  const elements = React.useMemo(() => ({\n    reference: referenceEl,\n    floating: floatingEl\n  }), [referenceEl, floatingEl]);\n  const floatingStyles = React.useMemo(() => {\n    const initialStyles = {\n      position: strategy,\n      left: 0,\n      top: 0\n    };\n    if (!elements.floating) {\n      return initialStyles;\n    }\n    const x = roundByDPR(elements.floating, data.x);\n    const y = roundByDPR(elements.floating, data.y);\n    if (transform) {\n      return {\n        ...initialStyles,\n        transform: \"translate(\" + x + \"px, \" + y + \"px)\",\n        ...(getDPR(elements.floating) >= 1.5 && {\n          willChange: 'transform'\n        })\n      };\n    }\n    return {\n      position: strategy,\n      left: x,\n      top: y\n    };\n  }, [strategy, transform, elements.floating, data.x, data.y]);\n  return React.useMemo(() => ({\n    ...data,\n    update,\n    refs,\n    elements,\n    floatingStyles\n  }), [data, update, refs, elements, floatingStyles]);\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow$1 = options => {\n  function isRef(value) {\n    return {}.hasOwnProperty.call(value, 'current');\n  }\n  return {\n    name: 'arrow',\n    options,\n    fn(state) {\n      const {\n        element,\n        padding\n      } = typeof options === 'function' ? options(state) : options;\n      if (element && isRef(element)) {\n        if (element.current != null) {\n          return arrow$2({\n            element: element.current,\n            padding\n          }).fn(state);\n        }\n        return {};\n      }\n      if (element) {\n        return arrow$2({\n          element,\n          padding\n        }).fn(state);\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = (options, deps) => ({\n  ...offset$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = (options, deps) => ({\n  ...shift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = (options, deps) => ({\n  ...limitShift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = (options, deps) => ({\n  ...flip$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = (options, deps) => ({\n  ...size$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = (options, deps) => ({\n  ...autoPlacement$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = (options, deps) => ({\n  ...hide$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = (options, deps) => ({\n  ...inline$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = (options, deps) => ({\n  ...arrow$1(options),\n  options: [options, deps]\n});\n\nexport { arrow, autoPlacement, flip, hide, inline, limitShift, offset, shift, size, useFloating };\n", "import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * Arrow\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'Arrow';\n\ntype ArrowElement = React.ElementRef<typeof Primitive.svg>;\ntype PrimitiveSvgProps = React.ComponentPropsWithoutRef<typeof Primitive.svg>;\ninterface ArrowProps extends PrimitiveSvgProps {}\n\nconst Arrow = React.forwardRef<ArrowElement, ArrowProps>((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return (\n    <Primitive.svg\n      {...arrowProps}\n      ref={forwardedRef}\n      width={width}\n      height={height}\n      viewBox=\"0 0 30 10\"\n      preserveAspectRatio=\"none\"\n    >\n      {/* We use their children if they're slotting to replace the whole svg */}\n      {props.asChild ? children : <polygon points=\"0,0 30,0 15,10\" />}\n    </Primitive.svg>\n  );\n});\n\nArrow.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Arrow;\n\nexport {\n  Arrow,\n  //\n  Root,\n};\nexport type { ArrowProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,SAAuB;;;ACKvB,IAAM,QAAQ,CAAC,OAAO,SAAS,UAAU,MAAM;AAC/C,IAAM,aAAa,CAAC,SAAS,KAAK;AAClC,IAAM,aAA0B,MAAM,OAAO,CAAC,KAAK,SAAS,IAAI,OAAO,MAAM,OAAO,MAAM,WAAW,CAAC,GAAG,OAAO,MAAM,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC;AACxI,IAAM,MAAM,KAAK;AACjB,IAAM,MAAM,KAAK;AACjB,IAAM,QAAQ,KAAK;AACnB,IAAM,QAAQ,KAAK;AACnB,IAAM,eAAe,QAAM;AAAA,EACzB,GAAG;AAAA,EACH,GAAG;AACL;AACA,IAAM,kBAAkB;AAAA,EACtB,MAAM;AAAA,EACN,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AACP;AACA,IAAM,uBAAuB;AAAA,EAC3B,OAAO;AAAA,EACP,KAAK;AACP;AACA,SAAS,MAAM,OAAO,OAAO,KAAK;AAChC,SAAO,IAAI,OAAO,IAAI,OAAO,GAAG,CAAC;AACnC;AACA,SAAS,SAAS,OAAO,OAAO;AAC9B,SAAO,OAAO,UAAU,aAAa,MAAM,KAAK,IAAI;AACtD;AACA,SAAS,QAAQ,WAAW;AAC1B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,aAAa,WAAW;AAC/B,SAAO,UAAU,MAAM,GAAG,EAAE,CAAC;AAC/B;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,SAAS,MAAM,MAAM;AAC9B;AACA,SAAS,cAAc,MAAM;AAC3B,SAAO,SAAS,MAAM,WAAW;AACnC;AACA,SAAS,YAAY,WAAW;AAC9B,SAAO,CAAC,OAAO,QAAQ,EAAE,SAAS,QAAQ,SAAS,CAAC,IAAI,MAAM;AAChE;AACA,SAAS,iBAAiB,WAAW;AACnC,SAAO,gBAAgB,YAAY,SAAS,CAAC;AAC/C;AACA,SAAS,kBAAkB,WAAW,OAAO,KAAK;AAChD,MAAI,QAAQ,QAAQ;AAClB,UAAM;AAAA,EACR;AACA,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,SAAS,cAAc,aAAa;AAC1C,MAAI,oBAAoB,kBAAkB,MAAM,eAAe,MAAM,QAAQ,WAAW,UAAU,SAAS,cAAc,UAAU,WAAW;AAC9I,MAAI,MAAM,UAAU,MAAM,IAAI,MAAM,SAAS,MAAM,GAAG;AACpD,wBAAoB,qBAAqB,iBAAiB;AAAA,EAC5D;AACA,SAAO,CAAC,mBAAmB,qBAAqB,iBAAiB,CAAC;AACpE;AACA,SAAS,sBAAsB,WAAW;AACxC,QAAM,oBAAoB,qBAAqB,SAAS;AACxD,SAAO,CAAC,8BAA8B,SAAS,GAAG,mBAAmB,8BAA8B,iBAAiB,CAAC;AACvH;AACA,SAAS,8BAA8B,WAAW;AAChD,SAAO,UAAU,QAAQ,cAAc,eAAa,qBAAqB,SAAS,CAAC;AACrF;AACA,SAAS,YAAY,MAAM,SAAS,KAAK;AACvC,QAAM,KAAK,CAAC,QAAQ,OAAO;AAC3B,QAAM,KAAK,CAAC,SAAS,MAAM;AAC3B,QAAM,KAAK,CAAC,OAAO,QAAQ;AAC3B,QAAM,KAAK,CAAC,UAAU,KAAK;AAC3B,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AACH,UAAI;AAAK,eAAO,UAAU,KAAK;AAC/B,aAAO,UAAU,KAAK;AAAA,IACxB,KAAK;AAAA,IACL,KAAK;AACH,aAAO,UAAU,KAAK;AAAA,IACxB;AACE,aAAO,CAAC;AAAA,EACZ;AACF;AACA,SAAS,0BAA0B,WAAW,eAAe,WAAW,KAAK;AAC3E,QAAM,YAAY,aAAa,SAAS;AACxC,MAAI,OAAO,YAAY,QAAQ,SAAS,GAAG,cAAc,SAAS,GAAG;AACrE,MAAI,WAAW;AACb,WAAO,KAAK,IAAI,UAAQ,OAAO,MAAM,SAAS;AAC9C,QAAI,eAAe;AACjB,aAAO,KAAK,OAAO,KAAK,IAAI,6BAA6B,CAAC;AAAA,IAC5D;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,qBAAqB,WAAW;AACvC,SAAO,UAAU,QAAQ,0BAA0B,UAAQ,gBAAgB,IAAI,CAAC;AAClF;AACA,SAAS,oBAAoB,SAAS;AACpC,SAAO;AAAA,IACL,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,GAAG;AAAA,EACL;AACF;AACA,SAAS,iBAAiB,SAAS;AACjC,SAAO,OAAO,YAAY,WAAW,oBAAoB,OAAO,IAAI;AAAA,IAClE,KAAK;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,MAAM;AAAA,EACR;AACF;AACA,SAAS,iBAAiB,MAAM;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,KAAK;AAAA,IACL,MAAM;AAAA,IACN,OAAO,IAAI;AAAA,IACX,QAAQ,IAAI;AAAA,IACZ;AAAA,IACA;AAAA,EACF;AACF;;;ACpIA,SAAS,2BAA2B,MAAM,WAAW,KAAK;AACxD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,WAAW,YAAY,SAAS;AACtC,QAAM,gBAAgB,iBAAiB,SAAS;AAChD,QAAM,cAAc,cAAc,aAAa;AAC/C,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,aAAa,aAAa;AAChC,QAAM,UAAU,UAAU,IAAI,UAAU,QAAQ,IAAI,SAAS,QAAQ;AACrE,QAAM,UAAU,UAAU,IAAI,UAAU,SAAS,IAAI,SAAS,SAAS;AACvE,QAAM,cAAc,UAAU,WAAW,IAAI,IAAI,SAAS,WAAW,IAAI;AACzE,MAAI;AACJ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,SAAS;AAAA,MAC5B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG;AAAA,QACH,GAAG,UAAU,IAAI,UAAU;AAAA,MAC7B;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,UAAU;AAAA,QAC3B,GAAG;AAAA,MACL;AACA;AAAA,IACF,KAAK;AACH,eAAS;AAAA,QACP,GAAG,UAAU,IAAI,SAAS;AAAA,QAC1B,GAAG;AAAA,MACL;AACA;AAAA,IACF;AACE,eAAS;AAAA,QACP,GAAG,UAAU;AAAA,QACb,GAAG,UAAU;AAAA,MACf;AAAA,EACJ;AACA,UAAQ,aAAa,SAAS,GAAG;AAAA,IAC/B,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,IACF,KAAK;AACH,aAAO,aAAa,KAAK,eAAe,OAAO,aAAa,KAAK;AACjE;AAAA,EACJ;AACA,SAAO;AACT;AASA,IAAM,kBAAkB,OAAO,WAAW,UAAU,WAAW;AAC7D,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa,CAAC;AAAA,IACd,UAAAC;AAAA,EACF,IAAI;AACJ,QAAM,kBAAkB,WAAW,OAAO,OAAO;AACjD,QAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,QAAQ;AAC5E,MAAI,QAAQ,MAAMA,UAAS,gBAAgB;AAAA,IACzC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI;AAAA,IACF;AAAA,IACA;AAAA,EACF,IAAI,2BAA2B,OAAO,WAAW,GAAG;AACpD,MAAI,oBAAoB;AACxB,MAAI,iBAAiB,CAAC;AACtB,MAAI,aAAa;AACjB,WAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAC/C,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,gBAAgB,CAAC;AACrB,UAAM;AAAA,MACJ,GAAG;AAAA,MACH,GAAG;AAAA,MACH;AAAA,MACA;AAAA,IACF,IAAI,MAAM,GAAG;AAAA,MACX;AAAA,MACA;AAAA,MACA,kBAAkB;AAAA,MAClB,WAAW;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAAA;AAAA,MACA,UAAU;AAAA,QACR;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AACD,QAAI,SAAS,OAAO,QAAQ;AAC5B,QAAI,SAAS,OAAO,QAAQ;AAC5B,qBAAiB;AAAA,MACf,GAAG;AAAA,MACH,CAAC,IAAI,GAAG;AAAA,QACN,GAAG,eAAe,IAAI;AAAA,QACtB,GAAG;AAAA,MACL;AAAA,IACF;AACA,QAAI,SAAS,cAAc,IAAI;AAC7B;AACA,UAAI,OAAO,UAAU,UAAU;AAC7B,YAAI,MAAM,WAAW;AACnB,8BAAoB,MAAM;AAAA,QAC5B;AACA,YAAI,MAAM,OAAO;AACf,kBAAQ,MAAM,UAAU,OAAO,MAAMA,UAAS,gBAAgB;AAAA,YAC5D;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC,IAAI,MAAM;AAAA,QACb;AACA,SAAC;AAAA,UACC;AAAA,UACA;AAAA,QACF,IAAI,2BAA2B,OAAO,mBAAmB,GAAG;AAAA,MAC9D;AACA,UAAI;AAAA,IACN;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,EACF;AACF;AAUA,eAAe,eAAe,OAAO,SAAS;AAC5C,MAAI;AACJ,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,UAAAA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ,WAAW;AAAA,IACX,eAAe;AAAA,IACf,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,UAAU;AAAA,EACZ,IAAI,SAAS,SAAS,KAAK;AAC3B,QAAM,gBAAgB,iBAAiB,OAAO;AAC9C,QAAM,aAAa,mBAAmB,aAAa,cAAc;AACjE,QAAM,UAAU,SAAS,cAAc,aAAa,cAAc;AAClE,QAAM,qBAAqB,iBAAiB,MAAMA,UAAS,gBAAgB;AAAA,IACzE,WAAW,wBAAwB,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,OAAO,OAAO,OAAO,wBAAwB,QAAQ,UAAU,QAAQ,kBAAmB,OAAOA,UAAS,sBAAsB,OAAO,SAASA,UAAS,mBAAmB,SAAS,QAAQ;AAAA,IAChS;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,CAAC;AACF,QAAM,OAAO,mBAAmB,aAAa;AAAA,IAC3C;AAAA,IACA;AAAA,IACA,OAAO,MAAM,SAAS;AAAA,IACtB,QAAQ,MAAM,SAAS;AAAA,EACzB,IAAI,MAAM;AACV,QAAM,eAAe,OAAOA,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,SAAS,QAAQ;AAClH,QAAM,cAAe,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,YAAY,KAAO,OAAOA,UAAS,YAAY,OAAO,SAASA,UAAS,SAAS,YAAY,MAAO;AAAA,IACvL,GAAG;AAAA,IACH,GAAG;AAAA,EACL,IAAI;AAAA,IACF,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,QAAM,oBAAoB,iBAAiBA,UAAS,wDAAwD,MAAMA,UAAS,sDAAsD;AAAA,IAC/K;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC,IAAI,IAAI;AACT,SAAO;AAAA,IACL,MAAM,mBAAmB,MAAM,kBAAkB,MAAM,cAAc,OAAO,YAAY;AAAA,IACxF,SAAS,kBAAkB,SAAS,mBAAmB,SAAS,cAAc,UAAU,YAAY;AAAA,IACpG,OAAO,mBAAmB,OAAO,kBAAkB,OAAO,cAAc,QAAQ,YAAY;AAAA,IAC5F,QAAQ,kBAAkB,QAAQ,mBAAmB,QAAQ,cAAc,SAAS,YAAY;AAAA,EAClG;AACF;AAOA,IAAM,QAAQ,cAAY;AAAA,EACxB,MAAM;AAAA,EACN;AAAA,EACA,MAAM,GAAG,OAAO;AACd,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,UAAAA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,UAAM;AAAA,MACJ;AAAA,MACA,UAAU;AAAA,IACZ,IAAI,SAAS,SAAS,KAAK,KAAK,CAAC;AACjC,QAAI,WAAW,MAAM;AACnB,aAAO,CAAC;AAAA,IACV;AACA,UAAM,gBAAgB,iBAAiB,OAAO;AAC9C,UAAM,SAAS;AAAA,MACb;AAAA,MACA;AAAA,IACF;AACA,UAAM,OAAO,iBAAiB,SAAS;AACvC,UAAM,SAAS,cAAc,IAAI;AACjC,UAAM,kBAAkB,MAAMA,UAAS,cAAc,OAAO;AAC5D,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,UAAU,UAAU,WAAW;AACrC,UAAM,aAAa,UAAU,iBAAiB;AAC9C,UAAM,UAAU,MAAM,UAAU,MAAM,IAAI,MAAM,UAAU,IAAI,IAAI,OAAO,IAAI,IAAI,MAAM,SAAS,MAAM;AACtG,UAAM,YAAY,OAAO,IAAI,IAAI,MAAM,UAAU,IAAI;AACrD,UAAM,oBAAoB,OAAOA,UAAS,mBAAmB,OAAO,SAASA,UAAS,gBAAgB,OAAO;AAC7G,QAAI,aAAa,oBAAoB,kBAAkB,UAAU,IAAI;AAGrE,QAAI,CAAC,cAAc,CAAE,OAAOA,UAAS,aAAa,OAAO,SAASA,UAAS,UAAU,iBAAiB,IAAK;AACzG,mBAAa,SAAS,SAAS,UAAU,KAAK,MAAM,SAAS,MAAM;AAAA,IACrE;AACA,UAAM,oBAAoB,UAAU,IAAI,YAAY;AAIpD,UAAM,yBAAyB,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9E,UAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AACrE,UAAM,aAAa,IAAI,cAAc,OAAO,GAAG,sBAAsB;AAIrE,UAAM,QAAQ;AACd,UAAMC,OAAM,aAAa,gBAAgB,MAAM,IAAI;AACnD,UAAM,SAAS,aAAa,IAAI,gBAAgB,MAAM,IAAI,IAAI;AAC9D,UAAMC,UAAS,MAAM,OAAO,QAAQD,IAAG;AAMvC,UAAM,kBAAkB,CAAC,eAAe,SAAS,aAAa,SAAS,KAAK,QAAQ,WAAWC,WAAU,MAAM,UAAU,MAAM,IAAI,KAAK,SAAS,QAAQ,aAAa,cAAc,gBAAgB,MAAM,IAAI,IAAI;AAClN,UAAM,kBAAkB,kBAAkB,SAAS,QAAQ,SAAS,QAAQ,SAASD,OAAM;AAC3F,WAAO;AAAA,MACL,CAAC,IAAI,GAAG,OAAO,IAAI,IAAI;AAAA,MACvB,MAAM;AAAA,QACJ,CAAC,IAAI,GAAGC;AAAA,QACR,cAAc,SAASA,UAAS;AAAA,QAChC,GAAI,mBAAmB;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAAA,MACA,OAAO;AAAA,IACT;AAAA,EACF;AACF;AA+GA,IAAM,OAAO,SAAU,SAAS;AAC9B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,UAAAC;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,UAAU,gBAAgB;AAAA,QAC1B,WAAW,iBAAiB;AAAA,QAC5B,oBAAoB;AAAA,QACpB,mBAAmB;AAAA,QACnB,4BAA4B;AAAA,QAC5B,gBAAgB;AAAA,QAChB,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAM3B,WAAK,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACnG,eAAO,CAAC;AAAA,MACV;AACA,YAAM,OAAO,QAAQ,SAAS;AAC9B,YAAM,kBAAkB,YAAY,gBAAgB;AACpD,YAAM,kBAAkB,QAAQ,gBAAgB,MAAM;AACtD,YAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,YAAM,qBAAqB,gCAAgC,mBAAmB,CAAC,gBAAgB,CAAC,qBAAqB,gBAAgB,CAAC,IAAI,sBAAsB,gBAAgB;AAChL,YAAM,+BAA+B,8BAA8B;AACnE,UAAI,CAAC,+BAA+B,8BAA8B;AAChE,2BAAmB,KAAK,GAAG,0BAA0B,kBAAkB,eAAe,2BAA2B,GAAG,CAAC;AAAA,MACvH;AACA,YAAMC,cAAa,CAAC,kBAAkB,GAAG,kBAAkB;AAC3D,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,YAAY,CAAC;AACnB,UAAI,kBAAkB,uBAAuB,eAAe,SAAS,OAAO,SAAS,qBAAqB,cAAc,CAAC;AACzH,UAAI,eAAe;AACjB,kBAAU,KAAK,SAAS,IAAI,CAAC;AAAA,MAC/B;AACA,UAAI,gBAAgB;AAClB,cAAMC,SAAQ,kBAAkB,WAAW,OAAO,GAAG;AACrD,kBAAU,KAAK,SAASA,OAAM,CAAC,CAAC,GAAG,SAASA,OAAM,CAAC,CAAC,CAAC;AAAA,MACvD;AACA,sBAAgB,CAAC,GAAG,eAAe;AAAA,QACjC;AAAA,QACA;AAAA,MACF,CAAC;AAGD,UAAI,CAAC,UAAU,MAAM,CAAAC,UAAQA,SAAQ,CAAC,GAAG;AACvC,YAAI,uBAAuB;AAC3B,cAAM,eAAe,wBAAwB,eAAe,SAAS,OAAO,SAAS,sBAAsB,UAAU,KAAK;AAC1H,cAAM,gBAAgBF,YAAW,SAAS;AAC1C,YAAI,eAAe;AACjB,cAAI;AACJ,gBAAM,0BAA0B,mBAAmB,cAAc,oBAAoB,YAAY,aAAa,IAAI;AAClH,gBAAM,+BAA+B,kBAAkB,cAAc,CAAC,MAAM,OAAO,SAAS,gBAAgB,UAAU,CAAC,KAAK;AAC5H,cAAI,CAAC,2BAA2B,4BAA4B;AAE1D,mBAAO;AAAA,cACL,MAAM;AAAA,gBACJ,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA,OAAO;AAAA,gBACL,WAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAIA,YAAI,kBAAkB,wBAAwB,cAAc,OAAO,OAAK,EAAE,UAAU,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,sBAAsB;AAG1L,YAAI,CAAC,gBAAgB;AACnB,kBAAQ,kBAAkB;AAAA,YACxB,KAAK,WACH;AACE,kBAAI;AACJ,oBAAMG,cAAa,yBAAyB,cAAc,OAAO,OAAK;AACpE,oBAAI,8BAA8B;AAChC,wBAAM,kBAAkB,YAAY,EAAE,SAAS;AAC/C,yBAAO,oBAAoB;AAAA;AAAA,kBAG3B,oBAAoB;AAAA,gBACtB;AACA,uBAAO;AAAA,cACT,CAAC,EAAE,IAAI,OAAK,CAAC,EAAE,WAAW,EAAE,UAAU,OAAO,CAAAC,cAAYA,YAAW,CAAC,EAAE,OAAO,CAAC,KAAKA,cAAa,MAAMA,WAAU,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,MAAM,OAAO,SAAS,uBAAuB,CAAC;AACjM,kBAAID,YAAW;AACb,iCAAiBA;AAAA,cACnB;AACA;AAAA,YACF;AAAA,YACF,KAAK;AACH,+BAAiB;AACjB;AAAA,UACJ;AAAA,QACF;AACA,YAAI,cAAc,gBAAgB;AAChC,iBAAO;AAAA,YACL,OAAO;AAAA,cACL,WAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;AAEA,SAAS,eAAe,UAAU,MAAM;AACtC,SAAO;AAAA,IACL,KAAK,SAAS,MAAM,KAAK;AAAA,IACzB,OAAO,SAAS,QAAQ,KAAK;AAAA,IAC7B,QAAQ,SAAS,SAAS,KAAK;AAAA,IAC/B,MAAM,SAAS,OAAO,KAAK;AAAA,EAC7B;AACF;AACA,SAAS,sBAAsB,UAAU;AACvC,SAAO,MAAM,KAAK,UAAQ,SAAS,IAAI,KAAK,CAAC;AAC/C;AAMA,IAAM,OAAO,SAAU,SAAS;AAC9B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,YAAM;AAAA,QACJ;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,WAAW;AAAA,QACX,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAC3B,cAAQ,UAAU;AAAA,QAChB,KAAK,mBACH;AACE,gBAAM,WAAW,MAAM,eAAe,OAAO;AAAA,YAC3C,GAAG;AAAA,YACH,gBAAgB;AAAA,UAClB,CAAC;AACD,gBAAM,UAAU,eAAe,UAAU,MAAM,SAAS;AACxD,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,wBAAwB;AAAA,cACxB,iBAAiB,sBAAsB,OAAO;AAAA,YAChD;AAAA,UACF;AAAA,QACF;AAAA,QACF,KAAK,WACH;AACE,gBAAM,WAAW,MAAM,eAAe,OAAO;AAAA,YAC3C,GAAG;AAAA,YACH,aAAa;AAAA,UACf,CAAC;AACD,gBAAM,UAAU,eAAe,UAAU,MAAM,QAAQ;AACvD,iBAAO;AAAA,YACL,MAAM;AAAA,cACJ,gBAAgB;AAAA,cAChB,SAAS,sBAAsB,OAAO;AAAA,YACxC;AAAA,UACF;AAAA,QACF;AAAA,QACF,SACE;AACE,iBAAO,CAAC;AAAA,QACV;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACF;AAwIA,eAAe,qBAAqB,OAAO,SAAS;AAClD,QAAM;AAAA,IACJ;AAAA,IACA,UAAAE;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,MAAM,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ;AACrF,QAAM,OAAO,QAAQ,SAAS;AAC9B,QAAM,YAAY,aAAa,SAAS;AACxC,QAAM,aAAa,YAAY,SAAS,MAAM;AAC9C,QAAM,gBAAgB,CAAC,QAAQ,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK;AAC5D,QAAM,iBAAiB,OAAO,aAAa,KAAK;AAChD,QAAM,WAAW,SAAS,SAAS,KAAK;AAGxC,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,OAAO,aAAa,WAAW;AAAA,IACjC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,eAAe;AAAA,EACjB,IAAI;AAAA,IACF,UAAU,SAAS,YAAY;AAAA,IAC/B,WAAW,SAAS,aAAa;AAAA,IACjC,eAAe,SAAS;AAAA,EAC1B;AACA,MAAI,aAAa,OAAO,kBAAkB,UAAU;AAClD,gBAAY,cAAc,QAAQ,gBAAgB,KAAK;AAAA,EACzD;AACA,SAAO,aAAa;AAAA,IAClB,GAAG,YAAY;AAAA,IACf,GAAG,WAAW;AAAA,EAChB,IAAI;AAAA,IACF,GAAG,WAAW;AAAA,IACd,GAAG,YAAY;AAAA,EACjB;AACF;AASA,IAAM,SAAS,SAAU,SAAS;AAChC,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,aAAa,MAAM,qBAAqB,OAAO,OAAO;AAI5D,UAAI,gBAAgB,wBAAwB,eAAe,WAAW,OAAO,SAAS,sBAAsB,eAAe,wBAAwB,eAAe,UAAU,QAAQ,sBAAsB,iBAAiB;AACzN,eAAO,CAAC;AAAA,MACV;AACA,aAAO;AAAA,QACL,GAAG,IAAI,WAAW;AAAA,QAClB,GAAG,IAAI,WAAW;AAAA,QAClB,MAAM;AAAA,UACJ,GAAG;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAOA,IAAM,QAAQ,SAAU,SAAS;AAC/B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,UAAU,gBAAgB;AAAA,QAC1B,WAAW,iBAAiB;AAAA,QAC5B,UAAU;AAAA,UACR,IAAI,UAAQ;AACV,gBAAI;AAAA,cACF,GAAAC;AAAA,cACA,GAAAC;AAAA,YACF,IAAI;AACJ,mBAAO;AAAA,cACL,GAAAD;AAAA,cACA,GAAAC;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAC3B,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,MACF;AACA,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,YAAY,YAAY,QAAQ,SAAS,CAAC;AAChD,YAAM,WAAW,gBAAgB,SAAS;AAC1C,UAAI,gBAAgB,OAAO,QAAQ;AACnC,UAAI,iBAAiB,OAAO,SAAS;AACrC,UAAI,eAAe;AACjB,cAAM,UAAU,aAAa,MAAM,QAAQ;AAC3C,cAAM,UAAU,aAAa,MAAM,WAAW;AAC9C,cAAMC,OAAM,gBAAgB,SAAS,OAAO;AAC5C,cAAMC,OAAM,gBAAgB,SAAS,OAAO;AAC5C,wBAAgB,MAAMD,MAAK,eAAeC,IAAG;AAAA,MAC/C;AACA,UAAI,gBAAgB;AAClB,cAAM,UAAU,cAAc,MAAM,QAAQ;AAC5C,cAAM,UAAU,cAAc,MAAM,WAAW;AAC/C,cAAMD,OAAM,iBAAiB,SAAS,OAAO;AAC7C,cAAMC,OAAM,iBAAiB,SAAS,OAAO;AAC7C,yBAAiB,MAAMD,MAAK,gBAAgBC,IAAG;AAAA,MACjD;AACA,YAAM,gBAAgB,QAAQ,GAAG;AAAA,QAC/B,GAAG;AAAA,QACH,CAAC,QAAQ,GAAG;AAAA,QACZ,CAAC,SAAS,GAAG;AAAA,MACf,CAAC;AACD,aAAO;AAAA,QACL,GAAG;AAAA,QACH,MAAM;AAAA,UACJ,GAAG,cAAc,IAAI;AAAA,UACrB,GAAG,cAAc,IAAI;AAAA,UACrB,SAAS;AAAA,YACP,CAAC,QAAQ,GAAG;AAAA,YACZ,CAAC,SAAS,GAAG;AAAA,UACf;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AAIA,IAAM,aAAa,SAAU,SAAS;AACpC,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL;AAAA,IACA,GAAG,OAAO;AACR,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,QAAAC,UAAS;AAAA,QACT,UAAU,gBAAgB;AAAA,QAC1B,WAAW,iBAAiB;AAAA,MAC9B,IAAI,SAAS,SAAS,KAAK;AAC3B,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,MACF;AACA,YAAM,YAAY,YAAY,SAAS;AACvC,YAAM,WAAW,gBAAgB,SAAS;AAC1C,UAAI,gBAAgB,OAAO,QAAQ;AACnC,UAAI,iBAAiB,OAAO,SAAS;AACrC,YAAM,YAAY,SAASA,SAAQ,KAAK;AACxC,YAAM,iBAAiB,OAAO,cAAc,WAAW;AAAA,QACrD,UAAU;AAAA,QACV,WAAW;AAAA,MACb,IAAI;AAAA,QACF,UAAU;AAAA,QACV,WAAW;AAAA,QACX,GAAG;AAAA,MACL;AACA,UAAI,eAAe;AACjB,cAAM,MAAM,aAAa,MAAM,WAAW;AAC1C,cAAM,WAAW,MAAM,UAAU,QAAQ,IAAI,MAAM,SAAS,GAAG,IAAI,eAAe;AAClF,cAAM,WAAW,MAAM,UAAU,QAAQ,IAAI,MAAM,UAAU,GAAG,IAAI,eAAe;AACnF,YAAI,gBAAgB,UAAU;AAC5B,0BAAgB;AAAA,QAClB,WAAW,gBAAgB,UAAU;AACnC,0BAAgB;AAAA,QAClB;AAAA,MACF;AACA,UAAI,gBAAgB;AAClB,YAAI,uBAAuB;AAC3B,cAAM,MAAM,aAAa,MAAM,UAAU;AACzC,cAAM,eAAe,CAAC,OAAO,MAAM,EAAE,SAAS,QAAQ,SAAS,CAAC;AAChE,cAAM,WAAW,MAAM,UAAU,SAAS,IAAI,MAAM,SAAS,GAAG,KAAK,iBAAiB,wBAAwB,eAAe,WAAW,OAAO,SAAS,sBAAsB,SAAS,MAAM,IAAI,MAAM,eAAe,IAAI,eAAe;AACzO,cAAM,WAAW,MAAM,UAAU,SAAS,IAAI,MAAM,UAAU,GAAG,KAAK,eAAe,MAAM,yBAAyB,eAAe,WAAW,OAAO,SAAS,uBAAuB,SAAS,MAAM,MAAM,eAAe,eAAe,YAAY;AACpP,YAAI,iBAAiB,UAAU;AAC7B,2BAAiB;AAAA,QACnB,WAAW,iBAAiB,UAAU;AACpC,2BAAiB;AAAA,QACnB;AAAA,MACF;AACA,aAAO;AAAA,QACL,CAAC,QAAQ,GAAG;AAAA,QACZ,CAAC,SAAS,GAAG;AAAA,MACf;AAAA,IACF;AAAA,EACF;AACF;AAQA,IAAM,OAAO,SAAU,SAAS;AAC9B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,MAAM,GAAG,OAAO;AACd,UAAI,uBAAuB;AAC3B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA,UAAAL;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM;AAAA,QACJ,QAAQ,MAAM;AAAA,QAAC;AAAA,QACf,GAAG;AAAA,MACL,IAAI,SAAS,SAAS,KAAK;AAC3B,YAAM,WAAW,MAAM,eAAe,OAAO,qBAAqB;AAClE,YAAM,OAAO,QAAQ,SAAS;AAC9B,YAAM,YAAY,aAAa,SAAS;AACxC,YAAM,UAAU,YAAY,SAAS,MAAM;AAC3C,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,MAAM;AACV,UAAI;AACJ,UAAI;AACJ,UAAI,SAAS,SAAS,SAAS,UAAU;AACvC,qBAAa;AACb,oBAAY,eAAgB,OAAOA,UAAS,SAAS,OAAO,SAASA,UAAS,MAAM,SAAS,QAAQ,KAAM,UAAU,SAAS,SAAS;AAAA,MACzI,OAAO;AACL,oBAAY;AACZ,qBAAa,cAAc,QAAQ,QAAQ;AAAA,MAC7C;AACA,YAAM,wBAAwB,SAAS,SAAS,MAAM,SAAS;AAC/D,YAAM,uBAAuB,QAAQ,SAAS,OAAO,SAAS;AAC9D,YAAM,0BAA0B,IAAI,SAAS,SAAS,UAAU,GAAG,qBAAqB;AACxF,YAAM,yBAAyB,IAAI,QAAQ,SAAS,SAAS,GAAG,oBAAoB;AACpF,YAAM,UAAU,CAAC,MAAM,eAAe;AACtC,UAAI,kBAAkB;AACtB,UAAI,iBAAiB;AACrB,WAAK,wBAAwB,MAAM,eAAe,UAAU,QAAQ,sBAAsB,QAAQ,GAAG;AACnG,yBAAiB;AAAA,MACnB;AACA,WAAK,yBAAyB,MAAM,eAAe,UAAU,QAAQ,uBAAuB,QAAQ,GAAG;AACrG,0BAAkB;AAAA,MACpB;AACA,UAAI,WAAW,CAAC,WAAW;AACzB,cAAM,OAAO,IAAI,SAAS,MAAM,CAAC;AACjC,cAAM,OAAO,IAAI,SAAS,OAAO,CAAC;AAClC,cAAM,OAAO,IAAI,SAAS,KAAK,CAAC;AAChC,cAAM,OAAO,IAAI,SAAS,QAAQ,CAAC;AACnC,YAAI,SAAS;AACX,2BAAiB,QAAQ,KAAK,SAAS,KAAK,SAAS,IAAI,OAAO,OAAO,IAAI,SAAS,MAAM,SAAS,KAAK;AAAA,QAC1G,OAAO;AACL,4BAAkB,SAAS,KAAK,SAAS,KAAK,SAAS,IAAI,OAAO,OAAO,IAAI,SAAS,KAAK,SAAS,MAAM;AAAA,QAC5G;AAAA,MACF;AACA,YAAM,MAAM;AAAA,QACV,GAAG;AAAA,QACH;AAAA,QACA;AAAA,MACF,CAAC;AACD,YAAM,iBAAiB,MAAMA,UAAS,cAAc,SAAS,QAAQ;AACrE,UAAI,UAAU,eAAe,SAAS,WAAW,eAAe,QAAQ;AACtE,eAAO;AAAA,UACL,OAAO;AAAA,YACL,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;;;ACnhCA,SAAS,YAAY;AACnB,SAAO,OAAO,WAAW;AAC3B;AACA,SAAS,YAAY,MAAM;AACzB,MAAI,OAAO,IAAI,GAAG;AAChB,YAAQ,KAAK,YAAY,IAAI,YAAY;AAAA,EAC3C;AAIA,SAAO;AACT;AACA,SAAS,UAAU,MAAM;AACvB,MAAI;AACJ,UAAQ,QAAQ,SAAS,sBAAsB,KAAK,kBAAkB,OAAO,SAAS,oBAAoB,gBAAgB;AAC5H;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAI;AACJ,UAAQ,QAAQ,OAAO,IAAI,IAAI,KAAK,gBAAgB,KAAK,aAAa,OAAO,aAAa,OAAO,SAAS,KAAK;AACjH;AACA,SAAS,OAAO,OAAO;AACrB,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,QAAQ,iBAAiB,UAAU,KAAK,EAAE;AACpE;AACA,SAAS,UAAU,OAAO;AACxB,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,WAAW,iBAAiB,UAAU,KAAK,EAAE;AACvE;AACA,SAAS,cAAc,OAAO;AAC5B,MAAI,CAAC,UAAU,GAAG;AAChB,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,eAAe,iBAAiB,UAAU,KAAK,EAAE;AAC3E;AACA,SAAS,aAAa,OAAO;AAC3B,MAAI,CAAC,UAAU,KAAK,OAAO,eAAe,aAAa;AACrD,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,cAAc,iBAAiB,UAAU,KAAK,EAAE;AAC1E;AACA,SAAS,kBAAkB,SAAS;AAClC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,OAAO;AAC5B,SAAO,kCAAkC,KAAK,WAAW,YAAY,SAAS,KAAK,CAAC,CAAC,UAAU,UAAU,EAAE,SAAS,OAAO;AAC7H;AACA,SAAS,eAAe,SAAS;AAC/B,SAAO,CAAC,SAAS,MAAM,IAAI,EAAE,SAAS,YAAY,OAAO,CAAC;AAC5D;AACA,SAAS,WAAW,SAAS;AAC3B,SAAO,CAAC,iBAAiB,QAAQ,EAAE,KAAK,cAAY;AAClD,QAAI;AACF,aAAO,QAAQ,QAAQ,QAAQ;AAAA,IACjC,SAAS,GAAG;AACV,aAAO;AAAA,IACT;AAAA,EACF,CAAC;AACH;AACA,SAAS,kBAAkB,cAAc;AACvC,QAAM,SAAS,SAAS;AACxB,QAAM,MAAM,UAAU,YAAY,IAAI,iBAAiB,YAAY,IAAI;AAIvE,SAAO,CAAC,aAAa,aAAa,SAAS,UAAU,aAAa,EAAE,KAAK,WAAS,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,SAAS,KAAK,MAAM,IAAI,gBAAgB,IAAI,kBAAkB,WAAW,UAAU,CAAC,WAAW,IAAI,iBAAiB,IAAI,mBAAmB,SAAS,UAAU,CAAC,WAAW,IAAI,SAAS,IAAI,WAAW,SAAS,UAAU,CAAC,aAAa,aAAa,SAAS,UAAU,eAAe,QAAQ,EAAE,KAAK,YAAU,IAAI,cAAc,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,SAAS,UAAU,UAAU,SAAS,EAAE,KAAK,YAAU,IAAI,WAAW,IAAI,SAAS,KAAK,CAAC;AACniB;AACA,SAAS,mBAAmB,SAAS;AACnC,MAAI,cAAc,cAAc,OAAO;AACvC,SAAO,cAAc,WAAW,KAAK,CAAC,sBAAsB,WAAW,GAAG;AACxE,QAAI,kBAAkB,WAAW,GAAG;AAClC,aAAO;AAAA,IACT,WAAW,WAAW,WAAW,GAAG;AAClC,aAAO;AAAA,IACT;AACA,kBAAc,cAAc,WAAW;AAAA,EACzC;AACA,SAAO;AACT;AACA,SAAS,WAAW;AAClB,MAAI,OAAO,QAAQ,eAAe,CAAC,IAAI;AAAU,WAAO;AACxD,SAAO,IAAI,SAAS,2BAA2B,MAAM;AACvD;AACA,SAAS,sBAAsB,MAAM;AACnC,SAAO,CAAC,QAAQ,QAAQ,WAAW,EAAE,SAAS,YAAY,IAAI,CAAC;AACjE;AACA,SAAS,iBAAiB,SAAS;AACjC,SAAO,UAAU,OAAO,EAAE,iBAAiB,OAAO;AACpD;AACA,SAAS,cAAc,SAAS;AAC9B,MAAI,UAAU,OAAO,GAAG;AACtB,WAAO;AAAA,MACL,YAAY,QAAQ;AAAA,MACpB,WAAW,QAAQ;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL,YAAY,QAAQ;AAAA,IACpB,WAAW,QAAQ;AAAA,EACrB;AACF;AACA,SAAS,cAAc,MAAM;AAC3B,MAAI,YAAY,IAAI,MAAM,QAAQ;AAChC,WAAO;AAAA,EACT;AACA,QAAM;AAAA;AAAA,IAEN,KAAK;AAAA,IAEL,KAAK;AAAA,IAEL,aAAa,IAAI,KAAK,KAAK;AAAA,IAE3B,mBAAmB,IAAI;AAAA;AACvB,SAAO,aAAa,MAAM,IAAI,OAAO,OAAO;AAC9C;AACA,SAAS,2BAA2B,MAAM;AACxC,QAAM,aAAa,cAAc,IAAI;AACrC,MAAI,sBAAsB,UAAU,GAAG;AACrC,WAAO,KAAK,gBAAgB,KAAK,cAAc,OAAO,KAAK;AAAA,EAC7D;AACA,MAAI,cAAc,UAAU,KAAK,kBAAkB,UAAU,GAAG;AAC9D,WAAO;AAAA,EACT;AACA,SAAO,2BAA2B,UAAU;AAC9C;AACA,SAAS,qBAAqB,MAAM,MAAM,iBAAiB;AACzD,MAAI;AACJ,MAAI,SAAS,QAAQ;AACnB,WAAO,CAAC;AAAA,EACV;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,qBAAqB,2BAA2B,IAAI;AAC1D,QAAM,SAAS,yBAAyB,uBAAuB,KAAK,kBAAkB,OAAO,SAAS,qBAAqB;AAC3H,QAAM,MAAM,UAAU,kBAAkB;AACxC,MAAI,QAAQ;AACV,UAAM,eAAe,gBAAgB,GAAG;AACxC,WAAO,KAAK,OAAO,KAAK,IAAI,kBAAkB,CAAC,GAAG,kBAAkB,kBAAkB,IAAI,qBAAqB,CAAC,GAAG,gBAAgB,kBAAkB,qBAAqB,YAAY,IAAI,CAAC,CAAC;AAAA,EAC9L;AACA,SAAO,KAAK,OAAO,oBAAoB,qBAAqB,oBAAoB,CAAC,GAAG,eAAe,CAAC;AACtG;AACA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,IAAI,UAAU,OAAO,eAAe,IAAI,MAAM,IAAI,IAAI,eAAe;AAC9E;;;AClJA,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAM,iBAAiB,OAAO;AAGpC,MAAI,QAAQ,WAAW,IAAI,KAAK,KAAK;AACrC,MAAI,SAAS,WAAW,IAAI,MAAM,KAAK;AACvC,QAAM,YAAY,cAAc,OAAO;AACvC,QAAM,cAAc,YAAY,QAAQ,cAAc;AACtD,QAAM,eAAe,YAAY,QAAQ,eAAe;AACxD,QAAM,iBAAiB,MAAM,KAAK,MAAM,eAAe,MAAM,MAAM,MAAM;AACzE,MAAI,gBAAgB;AAClB,YAAQ;AACR,aAAS;AAAA,EACX;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACL;AACF;AAEA,SAAS,cAAc,SAAS;AAC9B,SAAO,CAAC,UAAU,OAAO,IAAI,QAAQ,iBAAiB;AACxD;AAEA,SAAS,SAAS,SAAS;AACzB,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,CAAC,cAAc,UAAU,GAAG;AAC9B,WAAO,aAAa,CAAC;AAAA,EACvB;AACA,QAAM,OAAO,WAAW,sBAAsB;AAC9C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,UAAU;AAC/B,MAAI,KAAK,IAAI,MAAM,KAAK,KAAK,IAAI,KAAK,SAAS;AAC/C,MAAI,KAAK,IAAI,MAAM,KAAK,MAAM,IAAI,KAAK,UAAU;AAIjD,MAAI,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,GAAG;AAC7B,QAAI;AAAA,EACN;AACA,MAAI,CAAC,KAAK,CAAC,OAAO,SAAS,CAAC,GAAG;AAC7B,QAAI;AAAA,EACN;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,IAAM,YAAyB,aAAa,CAAC;AAC7C,SAAS,iBAAiB,SAAS;AACjC,QAAM,MAAM,UAAU,OAAO;AAC7B,MAAI,CAAC,SAAS,KAAK,CAAC,IAAI,gBAAgB;AACtC,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL,GAAG,IAAI,eAAe;AAAA,IACtB,GAAG,IAAI,eAAe;AAAA,EACxB;AACF;AACA,SAAS,uBAAuB,SAAS,SAAS,sBAAsB;AACtE,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AACA,MAAI,CAAC,wBAAwB,WAAW,yBAAyB,UAAU,OAAO,GAAG;AACnF,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,sBAAsB,SAAS,cAAc,iBAAiB,cAAc;AACnF,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,aAAa,QAAQ,sBAAsB;AACjD,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,QAAQ,aAAa,CAAC;AAC1B,MAAI,cAAc;AAChB,QAAI,cAAc;AAChB,UAAI,UAAU,YAAY,GAAG;AAC3B,gBAAQ,SAAS,YAAY;AAAA,MAC/B;AAAA,IACF,OAAO;AACL,cAAQ,SAAS,OAAO;AAAA,IAC1B;AAAA,EACF;AACA,QAAM,gBAAgB,uBAAuB,YAAY,iBAAiB,YAAY,IAAI,iBAAiB,UAAU,IAAI,aAAa,CAAC;AACvI,MAAI,KAAK,WAAW,OAAO,cAAc,KAAK,MAAM;AACpD,MAAI,KAAK,WAAW,MAAM,cAAc,KAAK,MAAM;AACnD,MAAI,QAAQ,WAAW,QAAQ,MAAM;AACrC,MAAI,SAAS,WAAW,SAAS,MAAM;AACvC,MAAI,YAAY;AACd,UAAM,MAAM,UAAU,UAAU;AAChC,UAAM,YAAY,gBAAgB,UAAU,YAAY,IAAI,UAAU,YAAY,IAAI;AACtF,QAAI,aAAa;AACjB,QAAI,gBAAgB,gBAAgB,UAAU;AAC9C,WAAO,iBAAiB,gBAAgB,cAAc,YAAY;AAChE,YAAM,cAAc,SAAS,aAAa;AAC1C,YAAM,aAAa,cAAc,sBAAsB;AACvD,YAAM,MAAM,iBAAiB,aAAa;AAC1C,YAAM,OAAO,WAAW,QAAQ,cAAc,aAAa,WAAW,IAAI,WAAW,KAAK,YAAY;AACtG,YAAM,MAAM,WAAW,OAAO,cAAc,YAAY,WAAW,IAAI,UAAU,KAAK,YAAY;AAClG,WAAK,YAAY;AACjB,WAAK,YAAY;AACjB,eAAS,YAAY;AACrB,gBAAU,YAAY;AACtB,WAAK;AACL,WAAK;AACL,mBAAa,UAAU,aAAa;AACpC,sBAAgB,gBAAgB,UAAU;AAAA,IAC5C;AAAA,EACF;AACA,SAAO,iBAAiB;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAIA,SAAS,oBAAoB,SAAS,MAAM;AAC1C,QAAM,aAAa,cAAc,OAAO,EAAE;AAC1C,MAAI,CAAC,MAAM;AACT,WAAO,sBAAsB,mBAAmB,OAAO,CAAC,EAAE,OAAO;AAAA,EACnE;AACA,SAAO,KAAK,OAAO;AACrB;AAEA,SAAS,cAAc,iBAAiB,QAAQ,kBAAkB;AAChE,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB;AAAA,EACrB;AACA,QAAM,WAAW,gBAAgB,sBAAsB;AACvD,QAAM,IAAI,SAAS,OAAO,OAAO,cAAc,mBAAmB;AAAA;AAAA,IAElE,oBAAoB,iBAAiB,QAAQ;AAAA;AAC7C,QAAM,IAAI,SAAS,MAAM,OAAO;AAChC,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,sDAAsD,MAAM;AACnE,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,aAAa;AAC7B,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,QAAM,WAAW,WAAW,WAAW,SAAS,QAAQ,IAAI;AAC5D,MAAI,iBAAiB,mBAAmB,YAAY,SAAS;AAC3D,WAAO;AAAA,EACT;AACA,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,MAAI,QAAQ,aAAa,CAAC;AAC1B,QAAM,UAAU,aAAa,CAAC;AAC9B,QAAM,0BAA0B,cAAc,YAAY;AAC1D,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAI,cAAc,YAAY,GAAG;AAC/B,YAAM,aAAa,sBAAsB,YAAY;AACrD,cAAQ,SAAS,YAAY;AAC7B,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C;AAAA,EACF;AACA,QAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,QAAQ,IAAI,IAAI,aAAa,CAAC;AAC1I,SAAO;AAAA,IACL,OAAO,KAAK,QAAQ,MAAM;AAAA,IAC1B,QAAQ,KAAK,SAAS,MAAM;AAAA,IAC5B,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,aAAa,MAAM,IAAI,QAAQ,IAAI,WAAW;AAAA,IAC3E,GAAG,KAAK,IAAI,MAAM,IAAI,OAAO,YAAY,MAAM,IAAI,QAAQ,IAAI,WAAW;AAAA,EAC5E;AACF;AAEA,SAAS,eAAe,SAAS;AAC/B,SAAO,MAAM,KAAK,QAAQ,eAAe,CAAC;AAC5C;AAIA,SAAS,gBAAgB,SAAS;AAChC,QAAM,OAAO,mBAAmB,OAAO;AACvC,QAAM,SAAS,cAAc,OAAO;AACpC,QAAM,OAAO,QAAQ,cAAc;AACnC,QAAM,QAAQ,IAAI,KAAK,aAAa,KAAK,aAAa,KAAK,aAAa,KAAK,WAAW;AACxF,QAAM,SAAS,IAAI,KAAK,cAAc,KAAK,cAAc,KAAK,cAAc,KAAK,YAAY;AAC7F,MAAI,IAAI,CAAC,OAAO,aAAa,oBAAoB,OAAO;AACxD,QAAM,IAAI,CAAC,OAAO;AAClB,MAAI,iBAAiB,IAAI,EAAE,cAAc,OAAO;AAC9C,SAAK,IAAI,KAAK,aAAa,KAAK,WAAW,IAAI;AAAA,EACjD;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,MAAM,UAAU,OAAO;AAC7B,QAAM,OAAO,mBAAmB,OAAO;AACvC,QAAM,iBAAiB,IAAI;AAC3B,MAAI,QAAQ,KAAK;AACjB,MAAI,SAAS,KAAK;AAClB,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,gBAAgB;AAClB,YAAQ,eAAe;AACvB,aAAS,eAAe;AACxB,UAAM,sBAAsB,SAAS;AACrC,QAAI,CAAC,uBAAuB,uBAAuB,aAAa,SAAS;AACvE,UAAI,eAAe;AACnB,UAAI,eAAe;AAAA,IACrB;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAGA,SAAS,2BAA2B,SAAS,UAAU;AACrD,QAAM,aAAa,sBAAsB,SAAS,MAAM,aAAa,OAAO;AAC5E,QAAM,MAAM,WAAW,MAAM,QAAQ;AACrC,QAAM,OAAO,WAAW,OAAO,QAAQ;AACvC,QAAM,QAAQ,cAAc,OAAO,IAAI,SAAS,OAAO,IAAI,aAAa,CAAC;AACzE,QAAM,QAAQ,QAAQ,cAAc,MAAM;AAC1C,QAAM,SAAS,QAAQ,eAAe,MAAM;AAC5C,QAAM,IAAI,OAAO,MAAM;AACvB,QAAM,IAAI,MAAM,MAAM;AACtB,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,kCAAkC,SAAS,kBAAkB,UAAU;AAC9E,MAAI;AACJ,MAAI,qBAAqB,YAAY;AACnC,WAAO,gBAAgB,SAAS,QAAQ;AAAA,EAC1C,WAAW,qBAAqB,YAAY;AAC1C,WAAO,gBAAgB,mBAAmB,OAAO,CAAC;AAAA,EACpD,WAAW,UAAU,gBAAgB,GAAG;AACtC,WAAO,2BAA2B,kBAAkB,QAAQ;AAAA,EAC9D,OAAO;AACL,UAAM,gBAAgB,iBAAiB,OAAO;AAC9C,WAAO;AAAA,MACL,GAAG,iBAAiB,IAAI,cAAc;AAAA,MACtC,GAAG,iBAAiB,IAAI,cAAc;AAAA,MACtC,OAAO,iBAAiB;AAAA,MACxB,QAAQ,iBAAiB;AAAA,IAC3B;AAAA,EACF;AACA,SAAO,iBAAiB,IAAI;AAC9B;AACA,SAAS,yBAAyB,SAAS,UAAU;AACnD,QAAM,aAAa,cAAc,OAAO;AACxC,MAAI,eAAe,YAAY,CAAC,UAAU,UAAU,KAAK,sBAAsB,UAAU,GAAG;AAC1F,WAAO;AAAA,EACT;AACA,SAAO,iBAAiB,UAAU,EAAE,aAAa,WAAW,yBAAyB,YAAY,QAAQ;AAC3G;AAKA,SAAS,4BAA4B,SAAS,OAAO;AACnD,QAAM,eAAe,MAAM,IAAI,OAAO;AACtC,MAAI,cAAc;AAChB,WAAO;AAAA,EACT;AACA,MAAI,SAAS,qBAAqB,SAAS,CAAC,GAAG,KAAK,EAAE,OAAO,QAAM,UAAU,EAAE,KAAK,YAAY,EAAE,MAAM,MAAM;AAC9G,MAAI,sCAAsC;AAC1C,QAAM,iBAAiB,iBAAiB,OAAO,EAAE,aAAa;AAC9D,MAAI,cAAc,iBAAiB,cAAc,OAAO,IAAI;AAG5D,SAAO,UAAU,WAAW,KAAK,CAAC,sBAAsB,WAAW,GAAG;AACpE,UAAM,gBAAgB,iBAAiB,WAAW;AAClD,UAAM,0BAA0B,kBAAkB,WAAW;AAC7D,QAAI,CAAC,2BAA2B,cAAc,aAAa,SAAS;AAClE,4CAAsC;AAAA,IACxC;AACA,UAAM,wBAAwB,iBAAiB,CAAC,2BAA2B,CAAC,sCAAsC,CAAC,2BAA2B,cAAc,aAAa,YAAY,CAAC,CAAC,uCAAuC,CAAC,YAAY,OAAO,EAAE,SAAS,oCAAoC,QAAQ,KAAK,kBAAkB,WAAW,KAAK,CAAC,2BAA2B,yBAAyB,SAAS,WAAW;AACzZ,QAAI,uBAAuB;AAEzB,eAAS,OAAO,OAAO,cAAY,aAAa,WAAW;AAAA,IAC7D,OAAO;AAEL,4CAAsC;AAAA,IACxC;AACA,kBAAc,cAAc,WAAW;AAAA,EACzC;AACA,QAAM,IAAI,SAAS,MAAM;AACzB,SAAO;AACT;AAIA,SAAS,gBAAgB,MAAM;AAC7B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,2BAA2B,aAAa,sBAAsB,WAAW,OAAO,IAAI,CAAC,IAAI,4BAA4B,SAAS,KAAK,EAAE,IAAI,CAAC,EAAE,OAAO,QAAQ;AACjK,QAAM,oBAAoB,CAAC,GAAG,0BAA0B,YAAY;AACpE,QAAM,wBAAwB,kBAAkB,CAAC;AACjD,QAAM,eAAe,kBAAkB,OAAO,CAAC,SAAS,qBAAqB;AAC3E,UAAM,OAAO,kCAAkC,SAAS,kBAAkB,QAAQ;AAClF,YAAQ,MAAM,IAAI,KAAK,KAAK,QAAQ,GAAG;AACvC,YAAQ,QAAQ,IAAI,KAAK,OAAO,QAAQ,KAAK;AAC7C,YAAQ,SAAS,IAAI,KAAK,QAAQ,QAAQ,MAAM;AAChD,YAAQ,OAAO,IAAI,KAAK,MAAM,QAAQ,IAAI;AAC1C,WAAO;AAAA,EACT,GAAG,kCAAkC,SAAS,uBAAuB,QAAQ,CAAC;AAC9E,SAAO;AAAA,IACL,OAAO,aAAa,QAAQ,aAAa;AAAA,IACzC,QAAQ,aAAa,SAAS,aAAa;AAAA,IAC3C,GAAG,aAAa;AAAA,IAChB,GAAG,aAAa;AAAA,EAClB;AACF;AAEA,SAAS,cAAc,SAAS;AAC9B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,iBAAiB,OAAO;AAC5B,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AAEA,SAAS,8BAA8B,SAAS,cAAc,UAAU;AACtE,QAAM,0BAA0B,cAAc,YAAY;AAC1D,QAAM,kBAAkB,mBAAmB,YAAY;AACvD,QAAM,UAAU,aAAa;AAC7B,QAAM,OAAO,sBAAsB,SAAS,MAAM,SAAS,YAAY;AACvE,MAAI,SAAS;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AACA,QAAM,UAAU,aAAa,CAAC;AAI9B,WAAS,4BAA4B;AACnC,YAAQ,IAAI,oBAAoB,eAAe;AAAA,EACjD;AACA,MAAI,2BAA2B,CAAC,2BAA2B,CAAC,SAAS;AACnE,QAAI,YAAY,YAAY,MAAM,UAAU,kBAAkB,eAAe,GAAG;AAC9E,eAAS,cAAc,YAAY;AAAA,IACrC;AACA,QAAI,yBAAyB;AAC3B,YAAM,aAAa,sBAAsB,cAAc,MAAM,SAAS,YAAY;AAClF,cAAQ,IAAI,WAAW,IAAI,aAAa;AACxC,cAAQ,IAAI,WAAW,IAAI,aAAa;AAAA,IAC1C,WAAW,iBAAiB;AAC1B,gCAA0B;AAAA,IAC5B;AAAA,EACF;AACA,MAAI,WAAW,CAAC,2BAA2B,iBAAiB;AAC1D,8BAA0B;AAAA,EAC5B;AACA,QAAM,aAAa,mBAAmB,CAAC,2BAA2B,CAAC,UAAU,cAAc,iBAAiB,MAAM,IAAI,aAAa,CAAC;AACpI,QAAM,IAAI,KAAK,OAAO,OAAO,aAAa,QAAQ,IAAI,WAAW;AACjE,QAAM,IAAI,KAAK,MAAM,OAAO,YAAY,QAAQ,IAAI,WAAW;AAC/D,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,OAAO,KAAK;AAAA,IACZ,QAAQ,KAAK;AAAA,EACf;AACF;AAEA,SAAS,mBAAmB,SAAS;AACnC,SAAO,iBAAiB,OAAO,EAAE,aAAa;AAChD;AAEA,SAAS,oBAAoB,SAAS,UAAU;AAC9C,MAAI,CAAC,cAAc,OAAO,KAAK,iBAAiB,OAAO,EAAE,aAAa,SAAS;AAC7E,WAAO;AAAA,EACT;AACA,MAAI,UAAU;AACZ,WAAO,SAAS,OAAO;AAAA,EACzB;AACA,MAAI,kBAAkB,QAAQ;AAM9B,MAAI,mBAAmB,OAAO,MAAM,iBAAiB;AACnD,sBAAkB,gBAAgB,cAAc;AAAA,EAClD;AACA,SAAO;AACT;AAIA,SAAS,gBAAgB,SAAS,UAAU;AAC1C,QAAM,MAAM,UAAU,OAAO;AAC7B,MAAI,WAAW,OAAO,GAAG;AACvB,WAAO;AAAA,EACT;AACA,MAAI,CAAC,cAAc,OAAO,GAAG;AAC3B,QAAI,kBAAkB,cAAc,OAAO;AAC3C,WAAO,mBAAmB,CAAC,sBAAsB,eAAe,GAAG;AACjE,UAAI,UAAU,eAAe,KAAK,CAAC,mBAAmB,eAAe,GAAG;AACtE,eAAO;AAAA,MACT;AACA,wBAAkB,cAAc,eAAe;AAAA,IACjD;AACA,WAAO;AAAA,EACT;AACA,MAAI,eAAe,oBAAoB,SAAS,QAAQ;AACxD,SAAO,gBAAgB,eAAe,YAAY,KAAK,mBAAmB,YAAY,GAAG;AACvF,mBAAe,oBAAoB,cAAc,QAAQ;AAAA,EAC3D;AACA,MAAI,gBAAgB,sBAAsB,YAAY,KAAK,mBAAmB,YAAY,KAAK,CAAC,kBAAkB,YAAY,GAAG;AAC/H,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,mBAAmB,OAAO,KAAK;AACxD;AAEA,IAAM,kBAAkB,eAAgB,MAAM;AAC5C,QAAM,oBAAoB,KAAK,mBAAmB;AAClD,QAAM,kBAAkB,KAAK;AAC7B,QAAM,qBAAqB,MAAM,gBAAgB,KAAK,QAAQ;AAC9D,SAAO;AAAA,IACL,WAAW,8BAA8B,KAAK,WAAW,MAAM,kBAAkB,KAAK,QAAQ,GAAG,KAAK,QAAQ;AAAA,IAC9G,UAAU;AAAA,MACR,GAAG;AAAA,MACH,GAAG;AAAA,MACH,OAAO,mBAAmB;AAAA,MAC1B,QAAQ,mBAAmB;AAAA,IAC7B;AAAA,EACF;AACF;AAEA,SAAS,MAAM,SAAS;AACtB,SAAO,iBAAiB,OAAO,EAAE,cAAc;AACjD;AAEA,IAAM,WAAW;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,SAAS,cAAc,GAAG,GAAG;AAC3B,SAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE;AAC7E;AAGA,SAAS,YAAY,SAAS,QAAQ;AACpC,MAAI,KAAK;AACT,MAAI;AACJ,QAAM,OAAO,mBAAmB,OAAO;AACvC,WAAS,UAAU;AACjB,QAAI;AACJ,iBAAa,SAAS;AACtB,KAAC,MAAM,OAAO,QAAQ,IAAI,WAAW;AACrC,SAAK;AAAA,EACP;AACA,WAAS,QAAQ,MAAM,WAAW;AAChC,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,QAAI,cAAc,QAAQ;AACxB,kBAAY;AAAA,IACd;AACA,YAAQ;AACR,UAAM,2BAA2B,QAAQ,sBAAsB;AAC/D,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,CAAC,MAAM;AACT,aAAO;AAAA,IACT;AACA,QAAI,CAAC,SAAS,CAAC,QAAQ;AACrB;AAAA,IACF;AACA,UAAM,WAAW,MAAM,GAAG;AAC1B,UAAM,aAAa,MAAM,KAAK,eAAe,OAAO,MAAM;AAC1D,UAAM,cAAc,MAAM,KAAK,gBAAgB,MAAM,OAAO;AAC5D,UAAM,YAAY,MAAM,IAAI;AAC5B,UAAM,aAAa,CAAC,WAAW,QAAQ,CAAC,aAAa,QAAQ,CAAC,cAAc,QAAQ,CAAC,YAAY;AACjG,UAAM,UAAU;AAAA,MACd;AAAA,MACA,WAAW,IAAI,GAAG,IAAI,GAAG,SAAS,CAAC,KAAK;AAAA,IAC1C;AACA,QAAI,gBAAgB;AACpB,aAAS,cAAc,SAAS;AAC9B,YAAM,QAAQ,QAAQ,CAAC,EAAE;AACzB,UAAI,UAAU,WAAW;AACvB,YAAI,CAAC,eAAe;AAClB,iBAAO,QAAQ;AAAA,QACjB;AACA,YAAI,CAAC,OAAO;AAGV,sBAAY,WAAW,MAAM;AAC3B,oBAAQ,OAAO,IAAI;AAAA,UACrB,GAAG,GAAI;AAAA,QACT,OAAO;AACL,kBAAQ,OAAO,KAAK;AAAA,QACtB;AAAA,MACF;AACA,UAAI,UAAU,KAAK,CAAC,cAAc,0BAA0B,QAAQ,sBAAsB,CAAC,GAAG;AAQ5F,gBAAQ;AAAA,MACV;AACA,sBAAgB;AAAA,IAClB;AAIA,QAAI;AACF,WAAK,IAAI,qBAAqB,eAAe;AAAA,QAC3C,GAAG;AAAA;AAAA,QAEH,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,IACH,SAAS,IAAI;AACX,WAAK,IAAI,qBAAqB,eAAe,OAAO;AAAA,IACtD;AACA,OAAG,QAAQ,OAAO;AAAA,EACpB;AACA,UAAQ,IAAI;AACZ,SAAO;AACT;AAUA,SAAS,WAAW,WAAW,UAAU,QAAQ,SAAS;AACxD,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,gBAAgB,OAAO,mBAAmB;AAAA,IAC1C,cAAc,OAAO,yBAAyB;AAAA,IAC9C,iBAAiB;AAAA,EACnB,IAAI;AACJ,QAAM,cAAc,cAAc,SAAS;AAC3C,QAAM,YAAY,kBAAkB,iBAAiB,CAAC,GAAI,cAAc,qBAAqB,WAAW,IAAI,CAAC,GAAI,GAAG,qBAAqB,QAAQ,CAAC,IAAI,CAAC;AACvJ,YAAU,QAAQ,cAAY;AAC5B,sBAAkB,SAAS,iBAAiB,UAAU,QAAQ;AAAA,MAC5D,SAAS;AAAA,IACX,CAAC;AACD,sBAAkB,SAAS,iBAAiB,UAAU,MAAM;AAAA,EAC9D,CAAC;AACD,QAAM,YAAY,eAAe,cAAc,YAAY,aAAa,MAAM,IAAI;AAClF,MAAI,iBAAiB;AACrB,MAAI,iBAAiB;AACrB,MAAI,eAAe;AACjB,qBAAiB,IAAI,eAAe,UAAQ;AAC1C,UAAI,CAAC,UAAU,IAAI;AACnB,UAAI,cAAc,WAAW,WAAW,eAAe,gBAAgB;AAGrE,uBAAe,UAAU,QAAQ;AACjC,6BAAqB,cAAc;AACnC,yBAAiB,sBAAsB,MAAM;AAC3C,cAAI;AACJ,WAAC,kBAAkB,mBAAmB,QAAQ,gBAAgB,QAAQ,QAAQ;AAAA,QAChF,CAAC;AAAA,MACH;AACA,aAAO;AAAA,IACT,CAAC;AACD,QAAI,eAAe,CAAC,gBAAgB;AAClC,qBAAe,QAAQ,WAAW;AAAA,IACpC;AACA,mBAAe,QAAQ,QAAQ;AAAA,EACjC;AACA,MAAI;AACJ,MAAI,cAAc,iBAAiB,sBAAsB,SAAS,IAAI;AACtE,MAAI,gBAAgB;AAClB,cAAU;AAAA,EACZ;AACA,WAAS,YAAY;AACnB,UAAM,cAAc,sBAAsB,SAAS;AACnD,QAAI,eAAe,CAAC,cAAc,aAAa,WAAW,GAAG;AAC3D,aAAO;AAAA,IACT;AACA,kBAAc;AACd,cAAU,sBAAsB,SAAS;AAAA,EAC3C;AACA,SAAO;AACP,SAAO,MAAM;AACX,QAAI;AACJ,cAAU,QAAQ,cAAY;AAC5B,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAC/D,wBAAkB,SAAS,oBAAoB,UAAU,MAAM;AAAA,IACjE,CAAC;AACD,iBAAa,QAAQ,UAAU;AAC/B,KAAC,mBAAmB,mBAAmB,QAAQ,iBAAiB,WAAW;AAC3E,qBAAiB;AACjB,QAAI,gBAAgB;AAClB,2BAAqB,OAAO;AAAA,IAC9B;AAAA,EACF;AACF;AAmBA,IAAMM,UAAS;AAef,IAAMC,SAAQ;AAQd,IAAMC,QAAO;AAQb,IAAMC,QAAO;AAOb,IAAMC,QAAO;AAOb,IAAMC,SAAQ;AAYd,IAAMC,cAAa;AAMnB,IAAMC,mBAAkB,CAAC,WAAW,UAAU,YAAY;AAIxD,QAAM,QAAQ,oBAAI,IAAI;AACtB,QAAM,gBAAgB;AAAA,IACpB;AAAA,IACA,GAAG;AAAA,EACL;AACA,QAAM,oBAAoB;AAAA,IACxB,GAAG,cAAc;AAAA,IACjB,IAAI;AAAA,EACN;AACA,SAAO,gBAAkB,WAAW,UAAU;AAAA,IAC5C,GAAG;AAAA,IACH,UAAU;AAAA,EACZ,CAAC;AACH;;;AClvBA,YAAuB;AACvB,mBAA2C;AAC3C,eAA0B;AAE1B,IAAI,QAAQ,OAAO,aAAa,cAAc,+BAAkB;AAIhE,SAAS,UAAU,GAAG,GAAG;AACvB,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,OAAO,GAAG;AACzB,WAAO;AAAA,EACT;AACA,MAAI,OAAO,MAAM,cAAc,EAAE,SAAS,MAAM,EAAE,SAAS,GAAG;AAC5D,WAAO;AAAA,EACT;AACA,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,KAAK,KAAK,OAAO,MAAM,UAAU;AACnC,QAAI,MAAM,QAAQ,CAAC,GAAG;AACpB,eAAS,EAAE;AACX,UAAI,WAAW,EAAE;AAAQ,eAAO;AAChC,WAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,YAAI,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG;AAC1B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,WAAO,OAAO,KAAK,CAAC;AACpB,aAAS,KAAK;AACd,QAAI,WAAW,OAAO,KAAK,CAAC,EAAE,QAAQ;AACpC,aAAO;AAAA,IACT;AACA,SAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,UAAI,CAAC,CAAC,EAAE,eAAe,KAAK,GAAG,KAAK,CAAC,CAAC,GAAG;AACvC,eAAO;AAAA,MACT;AAAA,IACF;AACA,SAAK,IAAI,QAAQ,QAAQ,KAAI;AAC3B,YAAM,MAAM,KAAK,CAAC;AAClB,UAAI,QAAQ,YAAY,EAAE,UAAU;AAClC;AAAA,MACF;AACA,UAAI,CAAC,UAAU,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG;AAC9B,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,MAAM,KAAK,MAAM;AAC1B;AAEA,SAAS,OAAO,SAAS;AACvB,MAAI,OAAO,WAAW,aAAa;AACjC,WAAO;AAAA,EACT;AACA,QAAM,MAAM,QAAQ,cAAc,eAAe;AACjD,SAAO,IAAI,oBAAoB;AACjC;AAEA,SAAS,WAAW,SAAS,OAAO;AAClC,QAAM,MAAM,OAAO,OAAO;AAC1B,SAAO,KAAK,MAAM,QAAQ,GAAG,IAAI;AACnC;AAEA,SAAS,aAAa,OAAO;AAC3B,QAAM,MAAY,aAAO,KAAK;AAC9B,QAAM,MAAM;AACV,QAAI,UAAU;AAAA,EAChB,CAAC;AACD,SAAO;AACT;AAMA,SAAS,YAAY,SAAS;AAC5B,MAAI,YAAY,QAAQ;AACtB,cAAU,CAAC;AAAA,EACb;AACA,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,aAAa,CAAC;AAAA,IACd,UAAAC;AAAA,IACA,UAAU;AAAA,MACR,WAAW;AAAA,MACX,UAAU;AAAA,IACZ,IAAI,CAAC;AAAA,IACL,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,CAAC,MAAM,OAAO,IAAU,eAAS;AAAA,IACrC,GAAG;AAAA,IACH,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA,gBAAgB,CAAC;AAAA,IACjB,cAAc;AAAA,EAChB,CAAC;AACD,QAAM,CAAC,kBAAkB,mBAAmB,IAAU,eAAS,UAAU;AACzE,MAAI,CAAC,UAAU,kBAAkB,UAAU,GAAG;AAC5C,wBAAoB,UAAU;AAAA,EAChC;AACA,QAAM,CAAC,YAAY,aAAa,IAAU,eAAS,IAAI;AACvD,QAAM,CAAC,WAAW,YAAY,IAAU,eAAS,IAAI;AACrD,QAAM,eAAqB,kBAAY,UAAQ;AAC7C,QAAI,SAAS,aAAa,SAAS;AACjC,mBAAa,UAAU;AACvB,oBAAc,IAAI;AAAA,IACpB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,cAAoB,kBAAY,UAAQ;AAC5C,QAAI,SAAS,YAAY,SAAS;AAChC,kBAAY,UAAU;AACtB,mBAAa,IAAI;AAAA,IACnB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,cAAc,qBAAqB;AACzC,QAAM,aAAa,oBAAoB;AACvC,QAAM,eAAqB,aAAO,IAAI;AACtC,QAAM,cAAoB,aAAO,IAAI;AACrC,QAAM,UAAgB,aAAO,IAAI;AACjC,QAAM,0BAA0B,wBAAwB;AACxD,QAAM,0BAA0B,aAAa,oBAAoB;AACjE,QAAM,cAAc,aAAaA,SAAQ;AACzC,QAAM,UAAU,aAAa,IAAI;AACjC,QAAM,SAAe,kBAAY,MAAM;AACrC,QAAI,CAAC,aAAa,WAAW,CAAC,YAAY,SAAS;AACjD;AAAA,IACF;AACA,UAAM,SAAS;AAAA,MACb;AAAA,MACA;AAAA,MACA,YAAY;AAAA,IACd;AACA,QAAI,YAAY,SAAS;AACvB,aAAO,WAAW,YAAY;AAAA,IAChC;AACA,IAAAC,iBAAgB,aAAa,SAAS,YAAY,SAAS,MAAM,EAAE,KAAK,CAAAC,UAAQ;AAC9E,YAAM,WAAW;AAAA,QACf,GAAGA;AAAA;AAAA;AAAA;AAAA;AAAA,QAKH,cAAc,QAAQ,YAAY;AAAA,MACpC;AACA,UAAI,aAAa,WAAW,CAAC,UAAU,QAAQ,SAAS,QAAQ,GAAG;AACjE,gBAAQ,UAAU;AAClB,QAAS,mBAAU,MAAM;AACvB,kBAAQ,QAAQ;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AAAA,EACH,GAAG,CAAC,kBAAkB,WAAW,UAAU,aAAa,OAAO,CAAC;AAChE,QAAM,MAAM;AACV,QAAI,SAAS,SAAS,QAAQ,QAAQ,cAAc;AAClD,cAAQ,QAAQ,eAAe;AAC/B,cAAQ,CAAAA,WAAS;AAAA,QACf,GAAGA;AAAA,QACH,cAAc;AAAA,MAChB,EAAE;AAAA,IACJ;AAAA,EACF,GAAG,CAAC,IAAI,CAAC;AACT,QAAM,eAAqB,aAAO,KAAK;AACvC,QAAM,MAAM;AACV,iBAAa,UAAU;AACvB,WAAO,MAAM;AACX,mBAAa,UAAU;AAAA,IACzB;AAAA,EACF,GAAG,CAAC,CAAC;AACL,QAAM,MAAM;AACV,QAAI;AAAa,mBAAa,UAAU;AACxC,QAAI;AAAY,kBAAY,UAAU;AACtC,QAAI,eAAe,YAAY;AAC7B,UAAI,wBAAwB,SAAS;AACnC,eAAO,wBAAwB,QAAQ,aAAa,YAAY,MAAM;AAAA,MACxE;AACA,aAAO;AAAA,IACT;AAAA,EACF,GAAG,CAAC,aAAa,YAAY,QAAQ,yBAAyB,uBAAuB,CAAC;AACtF,QAAM,OAAa,cAAQ,OAAO;AAAA,IAChC,WAAW;AAAA,IACX,UAAU;AAAA,IACV;AAAA,IACA;AAAA,EACF,IAAI,CAAC,cAAc,WAAW,CAAC;AAC/B,QAAM,WAAiB,cAAQ,OAAO;AAAA,IACpC,WAAW;AAAA,IACX,UAAU;AAAA,EACZ,IAAI,CAAC,aAAa,UAAU,CAAC;AAC7B,QAAM,iBAAuB,cAAQ,MAAM;AACzC,UAAM,gBAAgB;AAAA,MACpB,UAAU;AAAA,MACV,MAAM;AAAA,MACN,KAAK;AAAA,IACP;AACA,QAAI,CAAC,SAAS,UAAU;AACtB,aAAO;AAAA,IACT;AACA,UAAM,IAAI,WAAW,SAAS,UAAU,KAAK,CAAC;AAC9C,UAAM,IAAI,WAAW,SAAS,UAAU,KAAK,CAAC;AAC9C,QAAI,WAAW;AACb,aAAO;AAAA,QACL,GAAG;AAAA,QACH,WAAW,eAAe,IAAI,SAAS,IAAI;AAAA,QAC3C,GAAI,OAAO,SAAS,QAAQ,KAAK,OAAO;AAAA,UACtC,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,MACN,KAAK;AAAA,IACP;AAAA,EACF,GAAG,CAAC,UAAU,WAAW,SAAS,UAAU,KAAK,GAAG,KAAK,CAAC,CAAC;AAC3D,SAAa,cAAQ,OAAO;AAAA,IAC1B,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,MAAM,QAAQ,MAAM,UAAU,cAAc,CAAC;AACpD;AAQA,IAAM,UAAU,aAAW;AACzB,WAAS,MAAM,OAAO;AACpB,WAAO,CAAC,EAAE,eAAe,KAAK,OAAO,SAAS;AAAA,EAChD;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN;AAAA,IACA,GAAG,OAAO;AACR,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,OAAO,YAAY,aAAa,QAAQ,KAAK,IAAI;AACrD,UAAI,WAAW,MAAM,OAAO,GAAG;AAC7B,YAAI,QAAQ,WAAW,MAAM;AAC3B,iBAAOC,OAAQ;AAAA,YACb,SAAS,QAAQ;AAAA,YACjB;AAAA,UACF,CAAC,EAAE,GAAG,KAAK;AAAA,QACb;AACA,eAAO,CAAC;AAAA,MACV;AACA,UAAI,SAAS;AACX,eAAOA,OAAQ;AAAA,UACb;AAAA,UACA;AAAA,QACF,CAAC,EAAE,GAAG,KAAK;AAAA,MACb;AACA,aAAO,CAAC;AAAA,IACV;AAAA,EACF;AACF;AASA,IAAMC,UAAS,CAAC,SAAS,UAAU;AAAA,EACjC,GAAGA,QAAS,OAAO;AAAA,EACnB,SAAS,CAAC,SAAS,IAAI;AACzB;AAOA,IAAMC,SAAQ,CAAC,SAAS,UAAU;AAAA,EAChC,GAAGA,OAAQ,OAAO;AAAA,EAClB,SAAS,CAAC,SAAS,IAAI;AACzB;AAKA,IAAMC,cAAa,CAAC,SAAS,UAAU;AAAA,EACrC,GAAGA,YAAa,OAAO;AAAA,EACvB,SAAS,CAAC,SAAS,IAAI;AACzB;AAQA,IAAMC,QAAO,CAAC,SAAS,UAAU;AAAA,EAC/B,GAAGA,MAAO,OAAO;AAAA,EACjB,SAAS,CAAC,SAAS,IAAI;AACzB;AAQA,IAAMC,QAAO,CAAC,SAAS,UAAU;AAAA,EAC/B,GAAGA,MAAO,OAAO;AAAA,EACjB,SAAS,CAAC,SAAS,IAAI;AACzB;AAkBA,IAAMC,QAAO,CAAC,SAAS,UAAU;AAAA,EAC/B,GAAGA,MAAO,OAAO;AAAA,EACjB,SAAS,CAAC,SAAS,IAAI;AACzB;AAkBA,IAAMC,SAAQ,CAAC,SAAS,UAAU;AAAA,EAChC,GAAG,QAAQ,OAAO;AAAA,EAClB,SAAS,CAAC,SAAS,IAAI;AACzB;;;AC7WA,IAAAC,SAAuB;AAyBW,yBAAA;AAlBlC,IAAM,OAAO;AAMb,IAAM,QAAc,kBAAqC,CAAC,OAAO,iBAAiB;AAChF,QAAM,EAAE,UAAU,QAAQ,IAAI,SAAS,GAAG,GAAG,WAAW,IAAI;AAC5D,aACE;IAAC,UAAU;IAAV;MACE,GAAG;MACJ,KAAK;MACL;MACA;MACA,SAAQ;MACR,qBAAoB;MAGnB,UAAA,MAAM,UAAU,eAAW,wBAAC,WAAA,EAAQ,QAAO,iBAAA,CAAiB;IAAA;EAC/D;AAEJ,CAAC;AAED,MAAM,cAAc;AAIpB,IAAM,OAAO;;;ANkBT,IAAAC,sBAAA;AAlBJ,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,IAAI,mBAAmB,WAAW;AAM/E,IAAM,CAAC,gBAAgB,gBAAgB,IAAI,oBAAwC,WAAW;AAK9F,IAAM,SAAgC,CAAC,UAAoC;AACzE,QAAM,EAAE,eAAe,SAAS,IAAI;AACpC,QAAM,CAAC,QAAQ,SAAS,IAAU,gBAA4B,IAAI;AAClE,aACE,yBAAC,gBAAA,EAAe,OAAO,eAAe,QAAgB,gBAAgB,WACnE,SAAA,CACH;AAEJ;AAEA,OAAO,cAAc;AAMrB,IAAM,cAAc;AAQpB,IAAM,eAAqB;EACzB,CAAC,OAAuC,iBAAiB;AACvD,UAAM,EAAE,eAAe,YAAY,GAAG,YAAY,IAAI;AACtD,UAAM,UAAU,iBAAiB,aAAa,aAAa;AAC3D,UAAM,MAAY,cAA4B,IAAI;AAClD,UAAM,eAAe,gBAAgB,cAAc,GAAG;AAEhD,IAAA,iBAAU,MAAM;AAIpB,cAAQ,gBAAe,yCAAY,YAAW,IAAI,OAAO;IAC3D,CAAC;AAED,WAAO,aAAa,WAAO,yBAAC,UAAU,KAAV,EAAe,GAAG,aAAa,KAAK,aAAA,CAAc;EAChF;AACF;AAEA,aAAa,cAAc;AAM3B,IAAM,eAAe;AAUrB,IAAM,CAAC,uBAAuB,iBAAiB,IAC7C,oBAA+C,YAAY;AAoB7D,IAAM,gBAAsB;EAC1B,CAAC,OAAwC,iBAAiB;;AACxD,UAAM;MACJ;MACA,OAAO;MACP,aAAa;MACb,QAAQ;MACR,cAAc;MACd,eAAe;MACf,kBAAkB;MAClB,oBAAoB,CAAC;MACrB,kBAAkB,uBAAuB;MACzC,SAAS;MACT,mBAAmB;MACnB,yBAAyB;MACzB;MACA,GAAG;IACL,IAAI;AAEJ,UAAM,UAAU,iBAAiB,cAAc,aAAa;AAE5D,UAAM,CAAC,SAAS,UAAU,IAAU,gBAAgC,IAAI;AACxE,UAAM,eAAe,gBAAgB,cAAc,CAAC,SAAS,WAAW,IAAI,CAAC;AAE7E,UAAM,CAACC,QAAO,QAAQ,IAAU,gBAAiC,IAAI;AACrE,UAAM,YAAY,QAAQA,MAAK;AAC/B,UAAM,cAAa,uCAAW,UAAS;AACvC,UAAM,eAAc,uCAAW,WAAU;AAEzC,UAAM,mBAAoB,QAAQ,UAAU,WAAW,MAAM,QAAQ;AAErE,UAAM,mBACJ,OAAO,yBAAyB,WAC5B,uBACA,EAAE,KAAK,GAAG,OAAO,GAAG,QAAQ,GAAG,MAAM,GAAG,GAAG,qBAAqB;AAEtE,UAAM,WAAW,MAAM,QAAQ,iBAAiB,IAAI,oBAAoB,CAAC,iBAAiB;AAC1F,UAAM,wBAAwB,SAAS,SAAS;AAEhD,UAAM,wBAAwB;MAC5B,SAAS;MACT,UAAU,SAAS,OAAO,SAAS;;MAEnC,aAAa;IACf;AAEA,UAAM,EAAE,MAAM,gBAAgB,WAAW,cAAc,eAAe,IAAI,YAAY;;MAEpF,UAAU;MACV,WAAW;MACX,sBAAsB,IAAI,SAAS;AACjC,cAAM,UAAU,WAAW,GAAG,MAAM;UAClC,gBAAgB,2BAA2B;QAC7C,CAAC;AACD,eAAO;MACT;MACA,UAAU;QACR,WAAW,QAAQ;MACrB;MACA,YAAY;QACVC,QAAO,EAAE,UAAU,aAAa,aAAa,eAAe,YAAY,CAAC;QACzE,mBACEC,OAAM;UACJ,UAAU;UACV,WAAW;UACX,SAAS,WAAW,YAAYC,YAAW,IAAI;UAC/C,GAAG;QACL,CAAC;QACH,mBAAmBC,MAAK,EAAE,GAAG,sBAAsB,CAAC;QACpDC,MAAK;UACH,GAAG;UACH,OAAO,CAAC,EAAE,UAAU,OAAO,gBAAgB,gBAAgB,MAAM;AAC/D,kBAAM,EAAE,OAAO,aAAa,QAAQ,aAAa,IAAI,MAAM;AAC3D,kBAAM,eAAe,SAAS,SAAS;AACvC,yBAAa,YAAY,kCAAkC,GAAG,cAAc,IAAI;AAChF,yBAAa,YAAY,mCAAmC,GAAG,eAAe,IAAI;AAClF,yBAAa,YAAY,+BAA+B,GAAG,WAAW,IAAI;AAC1E,yBAAa,YAAY,gCAAgC,GAAG,YAAY,IAAI;UAC9E;QACF,CAAC;QACDL,UAASA,OAAgB,EAAE,SAASA,QAAO,SAAS,aAAa,CAAC;QAClE,gBAAgB,EAAE,YAAY,YAAY,CAAC;QAC3C,oBAAoBM,MAAK,EAAE,UAAU,mBAAmB,GAAG,sBAAsB,CAAC;MACpF;IACF,CAAC;AAED,UAAM,CAAC,YAAY,WAAW,IAAI,6BAA6B,SAAS;AAExE,UAAM,eAAe,eAAe,QAAQ;AAC5C,qBAAgB,MAAM;AACpB,UAAI,cAAc;AAChB;MACF;IACF,GAAG,CAAC,cAAc,YAAY,CAAC;AAE/B,UAAM,UAAS,oBAAe,UAAf,mBAAsB;AACrC,UAAM,UAAS,oBAAe,UAAf,mBAAsB;AACrC,UAAM,sBAAoB,oBAAe,UAAf,mBAAsB,kBAAiB;AAEjE,UAAM,CAAC,eAAe,gBAAgB,IAAU,gBAAiB;AACjE,qBAAgB,MAAM;AACpB,UAAI;AAAS,yBAAiB,OAAO,iBAAiB,OAAO,EAAE,MAAM;IACvE,GAAG,CAAC,OAAO,CAAC;AAEZ,eACE;MAAC;MAAA;QACC,KAAK,KAAK;QACV,qCAAkC;QAClC,OAAO;UACL,GAAG;UACH,WAAW,eAAe,eAAe,YAAY;;UACrD,UAAU;UACV,QAAQ;UACR,CAAC,iCAAwC,GAAG;aAC1C,oBAAe,oBAAf,mBAAgC;aAChC,oBAAe,oBAAf,mBAAgC;UAClC,EAAE,KAAK,GAAG;;;;UAKV,KAAI,oBAAe,SAAf,mBAAqB,oBAAmB;YAC1C,YAAY;YACZ,eAAe;UACjB;QACF;QAIA,KAAK,MAAM;QAEX,cAAA;UAAC;UAAA;YACC,OAAO;YACP;YACA,eAAe;YACf;YACA;YACA,iBAAiB;YAEjB,cAAA;cAAC,UAAU;cAAV;gBACC,aAAW;gBACX,cAAY;gBACX,GAAG;gBACJ,KAAK;gBACL,OAAO;kBACL,GAAG,aAAa;;;kBAGhB,WAAW,CAAC,eAAe,SAAS;gBACtC;cAAA;YACF;UAAA;QACF;MAAA;IACF;EAEJ;AACF;AAEA,cAAc,cAAc;AAM5B,IAAM,aAAa;AAEnB,IAAM,gBAAoC;EACxC,KAAK;EACL,OAAO;EACP,QAAQ;EACR,MAAM;AACR;AAMA,IAAM,cAAoB,kBAAiD,SAASC,aAClF,OACA,cACA;AACA,QAAM,EAAE,eAAe,GAAG,WAAW,IAAI;AACzC,QAAM,iBAAiB,kBAAkB,YAAY,aAAa;AAClE,QAAM,WAAW,cAAc,eAAe,UAAU;AAExD;;;;QAIE;MAAC;MAAA;QACC,KAAK,eAAe;QACpB,OAAO;UACL,UAAU;UACV,MAAM,eAAe;UACrB,KAAK,eAAe;UACpB,CAAC,QAAQ,GAAG;UACZ,iBAAiB;YACf,KAAK;YACL,OAAO;YACP,QAAQ;YACR,MAAM;UACR,EAAE,eAAe,UAAU;UAC3B,WAAW;YACT,KAAK;YACL,OAAO;YACP,QAAQ;YACR,MAAM;UACR,EAAE,eAAe,UAAU;UAC3B,YAAY,eAAe,kBAAkB,WAAW;QAC1D;QAEA,cAAA;UAAgB;UAAf;YACE,GAAG;YACJ,KAAK;YACL,OAAO;cACL,GAAG,WAAW;;cAEd,SAAS;YACX;UAAA;QACF;MAAA;IACF;;AAEJ,CAAC;AAED,YAAY,cAAc;AAI1B,SAAS,UAAa,OAA6B;AACjD,SAAO,UAAU;AACnB;AAEA,IAAM,kBAAkB,CAAC,aAAsE;EAC7F,MAAM;EACN;EACA,GAAG,MAAM;;AACP,UAAM,EAAE,WAAW,OAAO,eAAe,IAAI;AAE7C,UAAM,sBAAoB,oBAAe,UAAf,mBAAsB,kBAAiB;AACjE,UAAM,gBAAgB;AACtB,UAAM,aAAa,gBAAgB,IAAI,QAAQ;AAC/C,UAAM,cAAc,gBAAgB,IAAI,QAAQ;AAEhD,UAAM,CAAC,YAAY,WAAW,IAAI,6BAA6B,SAAS;AACxE,UAAM,eAAe,EAAE,OAAO,MAAM,QAAQ,OAAO,KAAK,OAAO,EAAE,WAAW;AAE5E,UAAM,kBAAgB,oBAAe,UAAf,mBAAsB,MAAK,KAAK,aAAa;AACnE,UAAM,kBAAgB,oBAAe,UAAf,mBAAsB,MAAK,KAAK,cAAc;AAEpE,QAAI,IAAI;AACR,QAAI,IAAI;AAER,QAAI,eAAe,UAAU;AAC3B,UAAI,gBAAgB,eAAe,GAAG,YAAY;AAClD,UAAI,GAAG,CAAC,WAAW;IACrB,WAAW,eAAe,OAAO;AAC/B,UAAI,gBAAgB,eAAe,GAAG,YAAY;AAClD,UAAI,GAAG,MAAM,SAAS,SAAS,WAAW;IAC5C,WAAW,eAAe,SAAS;AACjC,UAAI,GAAG,CAAC,WAAW;AACnB,UAAI,gBAAgB,eAAe,GAAG,YAAY;IACpD,WAAW,eAAe,QAAQ;AAChC,UAAI,GAAG,MAAM,SAAS,QAAQ,WAAW;AACzC,UAAI,gBAAgB,eAAe,GAAG,YAAY;IACpD;AACA,WAAO,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE;EAC1B;AACF;AAEA,SAAS,6BAA6B,WAAsB;AAC1D,QAAM,CAAC,MAAM,QAAQ,QAAQ,IAAI,UAAU,MAAM,GAAG;AACpD,SAAO,CAAC,MAAc,KAAc;AACtC;AAEA,IAAMC,QAAO;AACb,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAMC,SAAQ;", "names": ["React", "platform", "max", "offset", "platform", "placements", "sides", "side", "placement", "overflow", "platform", "x", "y", "min", "max", "offset", "offset", "shift", "flip", "size", "hide", "arrow", "limitShift", "computePosition", "platform", "computePosition", "data", "arrow", "offset", "shift", "limitShift", "flip", "size", "hide", "arrow", "React", "import_jsx_runtime", "arrow", "offset", "shift", "limitShift", "flip", "size", "hide", "PopperArrow", "Root", "Arrow"]}