import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, XCircle, AlertCircle, Database } from 'lucide-react';
import { supabase } from '@/lib/supabaseClient';
import { useAuth } from '@/contexts/AuthContext';

export default function DatabaseSetupChecker() {
  const [checking, setChecking] = useState(false);
  const [results, setResults] = useState(null);
  const { currentUser } = useAuth();

  const checkDatabaseSetup = async () => {
    setChecking(true);
    const checkResults = {
      connection: false,
      authentication: false,
      notebooksTable: false,
      notesTable: false,
      userProfilesTable: false,
      rlsPolicies: false,
      permissions: false
    };

    try {
      // Check 1: Supabase connection
      if (supabase) {
        checkResults.connection = true;
      }

      // Check 2: User authentication
      if (currentUser?.id) {
        checkResults.authentication = true;
      }

      // Check 3: Test notebooks table
      try {
        const { error: notebooksError } = await supabase
          .from('notebooks')
          .select('id')
          .limit(1);
        
        if (!notebooksError) {
          checkResults.notebooksTable = true;
        }
      } catch (error) {
        console.log('Notebooks table check failed:', error);
      }

      // Check 4: Test notes table
      try {
        const { error: notesError } = await supabase
          .from('notes')
          .select('id')
          .limit(1);
        
        if (!notesError) {
          checkResults.notesTable = true;
        }
      } catch (error) {
        console.log('Notes table check failed:', error);
      }

      // Check 5: Test user_profiles table
      try {
        const { error: profilesError } = await supabase
          .from('user_profiles')
          .select('id')
          .limit(1);
        
        if (!profilesError) {
          checkResults.userProfilesTable = true;
        }
      } catch (error) {
        console.log('User profiles table check failed:', error);
      }

      // Check 6: Test RLS policies (try to insert without proper user)
      if (currentUser?.id && checkResults.notebooksTable) {
        try {
          const { error: permissionError } = await supabase
            .from('notebooks')
            .select('*')
            .eq('user_id', currentUser.id)
            .limit(1);
          
          if (!permissionError) {
            checkResults.permissions = true;
            checkResults.rlsPolicies = true;
          }
        } catch (error) {
          console.log('Permission check failed:', error);
        }
      }

    } catch (error) {
      console.error('Database setup check failed:', error);
    }

    setResults(checkResults);
    setChecking(false);
  };

  const getStatusIcon = (status) => {
    if (status) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    } else {
      return <XCircle className="h-5 w-5 text-red-500" />;
    }
  };

  const getOverallStatus = () => {
    if (!results) return null;
    
    const allChecks = Object.values(results);
    const passedChecks = allChecks.filter(Boolean).length;
    const totalChecks = allChecks.length;
    
    if (passedChecks === totalChecks) {
      return { status: 'success', message: 'All checks passed! Database is properly set up.' };
    } else if (passedChecks === 0) {
      return { status: 'error', message: 'Database setup is required. Please run the schema setup.' };
    } else {
      return { status: 'warning', message: `${passedChecks}/${totalChecks} checks passed. Some setup is required.` };
    }
  };

  const overallStatus = getOverallStatus();

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-6 w-6" />
          Database Setup Checker
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button 
          onClick={checkDatabaseSetup} 
          disabled={checking}
          className="w-full"
        >
          {checking ? 'Checking...' : 'Check Database Setup'}
        </Button>

        {results && (
          <div className="space-y-3">
            {overallStatus && (
              <div className={`p-3 rounded-lg flex items-center gap-2 ${
                overallStatus.status === 'success' ? 'bg-green-50 text-green-700' :
                overallStatus.status === 'warning' ? 'bg-yellow-50 text-yellow-700' :
                'bg-red-50 text-red-700'
              }`}>
                {overallStatus.status === 'success' && <CheckCircle className="h-5 w-5" />}
                {overallStatus.status === 'warning' && <AlertCircle className="h-5 w-5" />}
                {overallStatus.status === 'error' && <XCircle className="h-5 w-5" />}
                {overallStatus.message}
              </div>
            )}

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span>Supabase Connection</span>
                {getStatusIcon(results.connection)}
              </div>
              
              <div className="flex items-center justify-between">
                <span>User Authentication</span>
                {getStatusIcon(results.authentication)}
              </div>
              
              <div className="flex items-center justify-between">
                <span>Notebooks Table</span>
                {getStatusIcon(results.notebooksTable)}
              </div>
              
              <div className="flex items-center justify-between">
                <span>Notes Table</span>
                {getStatusIcon(results.notesTable)}
              </div>
              
              <div className="flex items-center justify-between">
                <span>User Profiles Table</span>
                {getStatusIcon(results.userProfilesTable)}
              </div>
              
              <div className="flex items-center justify-between">
                <span>RLS Policies</span>
                {getStatusIcon(results.rlsPolicies)}
              </div>
              
              <div className="flex items-center justify-between">
                <span>User Permissions</span>
                {getStatusIcon(results.permissions)}
              </div>
            </div>

            {!results.notebooksTable && (
              <div className="p-3 bg-blue-50 text-blue-700 rounded-lg">
                <p className="font-medium">Setup Required:</p>
                <p className="text-sm mt-1">
                  Please run the database schema from <code>database/schema.sql</code> in your Supabase SQL Editor.
                </p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
