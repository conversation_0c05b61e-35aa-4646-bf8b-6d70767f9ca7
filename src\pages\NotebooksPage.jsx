import React, { useState, useEffect } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  <PERSON><PERSON><PERSON><PERSON>,
  CardFooter,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  PlusCircle,
  BookOpen,
  Trash2,
  Edit3,
  Search,
  Calendar,
  Tag,
  SortAsc,
  SortDesc,
  Filter,
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { cn } from "@/lib/utils";
import { supabase } from "@/lib/supabaseClient";
import { useAuth } from "@/contexts/AuthContext";

const initialNotebooks = [
  {
    id: "nb1",
    name: "Work Projects",
    noteCount: 2,
    lastModified: "2025-05-21T10:00:00Z",
    color: "bg-blue-500",
  },
  {
    id: "nb2",
    name: "Personal Errands",
    noteCount: 1,
    lastModified: "2025-05-19T08:15:00Z",
    color: "bg-green-500",
  },
  {
    id: "nb3",
    name: "Book Summaries",
    noteCount: 1,
    lastModified: "2025-05-22T11:00:00Z",
    color: "bg-purple-500",
  },
  {
    id: "nb4",
    name: "Ideas & Brainstorming",
    noteCount: 0,
    lastModified: "2025-05-18T11:00:00Z",
    color: "bg-yellow-500",
  },
];

const NotebookCard = ({ notebook, onDelete, onEdit }) => {
  const navigate = useNavigate();
  return (
    <motion.div
      layout
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      transition={{ duration: 0.3 }}
    >
      <Card
        className="rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out bg-white dark:bg-slate-800/80 border-slate-200 dark:border-slate-700/60 overflow-hidden group"
        onClick={() => navigate(`/notebooks/${notebook.id}`)}
      >
        {/* Cover Image */}
        <div className="relative h-32 overflow-hidden">
          {notebook.cover_type === "color" ? (
            // Color cover
            <div
              className={cn("w-full h-full", notebook.color || "bg-blue-500")}
            />
          ) : (
            // Image cover
            <>
              <img
                src={notebook.cover_image || "/default-notebook-cover.svg"}
                alt={`${notebook.title} cover`}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                onError={(e) => {
                  e.target.src = "/default-notebook-cover.svg";
                }}
              />
              {/* Subtle overlay for image covers */}
              <div className="absolute inset-0 bg-gradient-to-br from-transparent to-black/10" />
            </>
          )}

          {/* Action Buttons */}
          <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 bg-white/20 hover:bg-white/40 text-white backdrop-blur-sm"
              onClick={(e) => {
                e.stopPropagation();
                onEdit(notebook);
              }}
              title="Edit Notebook"
            >
              <Edit3 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 bg-white/20 hover:bg-white/40 text-white backdrop-blur-sm"
              onClick={(e) => {
                e.stopPropagation();
                onDelete(notebook.id);
              }}
              title="Delete Notebook"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>

          {/* Notebook Icon */}
          <BookOpen className="h-8 w-8 text-white/90 absolute bottom-3 left-3 drop-shadow-lg" />
        </div>
        <CardContent className="p-4 cursor-pointer">
          <CardTitle className="text-lg font-semibold text-slate-800 dark:text-slate-100 hover:text-primary dark:hover:text-primary-dark transition-colors line-clamp-2 mb-1">
            {notebook.title}
          </CardTitle>
          <p className="text-sm text-slate-500 dark:text-slate-400 line-clamp-2">
            {notebook.description}
          </p>
          <div className="flex flex-wrap gap-1 mt-2">
            {notebook.tags &&
              notebook.tags.map((tag, idx) => (
                <span
                  key={idx}
                  className="text-xs bg-slate-200 dark:bg-slate-700 text-slate-600 dark:text-slate-300 rounded px-2 py-0.5"
                >
                  {tag}
                </span>
              ))}
          </div>
          <p className="text-xs text-slate-400 dark:text-slate-500 mt-2">
            Created:{" "}
            {notebook.created_at
              ? new Date(notebook.created_at).toLocaleDateString()
              : "-"}
          </p>
          <p className="text-xs text-slate-400 dark:text-slate-500">
            Updated:{" "}
            {notebook.updated_at
              ? new Date(notebook.updated_at).toLocaleDateString()
              : "-"}
          </p>
        </CardContent>
      </Card>
    </motion.div>
  );
};

const NotebookDialog = ({ open, onOpenChange, onSubmit, notebookToEdit }) => {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [tags, setTags] = useState(""); // comma separated
  const [color, setColor] = useState("bg-blue-500");

  const [coverType, setCoverType] = useState("image"); // 'image' or 'color'
  const [customImageUrl, setCustomImageUrl] = useState("");

  const colors = [
    "bg-blue-500",
    "bg-green-500",
    "bg-purple-500",
    "bg-yellow-500",
    "bg-red-500",
    "bg-indigo-500",
    "bg-pink-500",
    "bg-teal-500",
    "bg-orange-500",
    "bg-cyan-500",
    "bg-emerald-500",
    "bg-violet-500",
  ];

  useEffect(() => {
    if (notebookToEdit) {
      setName(notebookToEdit.title || "");
      setDescription(notebookToEdit.description || "");
      setTags((notebookToEdit.tags || []).join(", "));
      setColor(notebookToEdit.color || "bg-blue-500");
      setCoverType(notebookToEdit.cover_type || "image");

      setCustomImageUrl(
        notebookToEdit.cover_image !== "/default-notebook-cover.svg"
          ? notebookToEdit.cover_image || ""
          : ""
      );
    } else {
      setName("");
      setDescription("");
      setTags("");
      setColor("bg-blue-500");
      setCoverType("image");

      setCustomImageUrl("");
    }
  }, [notebookToEdit, open]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!name.trim()) return;
    // Determine the final cover image based on cover type
    const finalCoverImage =
      coverType === "color"
        ? "/default-notebook-cover.svg"
        : customImageUrl.trim() || "/default-notebook-cover.svg";

    onSubmit({
      ...notebookToEdit,
      title: name,
      description,
      tags: tags
        .split(",")
        .map((t) => t.trim())
        .filter(Boolean),
      color,
      cover_image: finalCoverImage,
      cover_type: coverType,
    });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px] bg-white dark:bg-slate-800">
        <DialogHeader>
          <DialogTitle className="text-slate-800 dark:text-slate-100">
            {notebookToEdit ? "Edit Notebook" : "Create New Notebook"}
          </DialogTitle>
          <DialogDescription className="text-slate-600 dark:text-slate-400">
            {notebookToEdit
              ? "Update the details for your notebook."
              : "Give your new notebook a name and choose a color."}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label
                htmlFor="name"
                className="text-right text-slate-700 dark:text-slate-300"
              >
                Name
              </Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="col-span-3 bg-slate-100 dark:bg-slate-700 border-slate-300 dark:border-slate-600"
                placeholder="Notebook Name"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label
                htmlFor="description"
                className="text-right text-slate-700 dark:text-slate-300"
              >
                Description
              </Label>
              <Input
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="col-span-3 bg-slate-100 dark:bg-slate-700 border-slate-300 dark:border-slate-600"
                placeholder="Description"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label
                htmlFor="tags"
                className="text-right text-slate-700 dark:text-slate-300"
              >
                Tags
              </Label>
              <Input
                id="tags"
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                className="col-span-3 bg-slate-100 dark:bg-slate-700 border-slate-300 dark:border-slate-600"
                placeholder="Comma separated tags"
              />
            </div>
            {/* Cover Type Selection */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right text-slate-700 dark:text-slate-300">
                Cover Type
              </Label>
              <div className="col-span-3 flex gap-2">
                <Button
                  type="button"
                  variant={coverType === "color" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCoverType("color")}
                  className="flex-1"
                >
                  Color
                </Button>
                <Button
                  type="button"
                  variant={coverType === "image" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCoverType("image")}
                  className="flex-1"
                >
                  Image
                </Button>
              </div>
            </div>

            {/* Color Selection (when cover type is color) */}
            {coverType === "color" && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right text-slate-700 dark:text-slate-300">
                  Color
                </Label>
                <div className="col-span-3 flex flex-wrap gap-2">
                  {colors.map((c) => (
                    <button
                      key={c}
                      type="button"
                      onClick={() => setColor(c)}
                      className={cn(
                        "h-8 w-8 rounded-full border-2 transition-all",
                        c,
                        color === c
                          ? "border-primary ring-2 ring-primary ring-offset-2"
                          : "border-transparent hover:border-slate-400"
                      )}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Image URL Input (when cover type is image) */}
            {coverType === "image" && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label
                  htmlFor="imageUrl"
                  className="text-right text-slate-700 dark:text-slate-300"
                >
                  Image URL
                </Label>
                <div className="col-span-3 space-y-2">
                  <Input
                    id="imageUrl"
                    value={customImageUrl}
                    onChange={(e) => setCustomImageUrl(e.target.value)}
                    className="bg-slate-100 dark:bg-slate-700 border-slate-300 dark:border-slate-600"
                    placeholder="https://example.com/image.jpg (optional)"
                  />
                  <p className="text-xs text-slate-500 dark:text-slate-400">
                    Leave empty to use default notebook cover
                  </p>
                </div>
              </div>
            )}

            {/* Preview */}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right text-slate-700 dark:text-slate-300">
                Preview
              </Label>
              <div className="col-span-3">
                <div className="relative w-24 h-12 rounded-lg overflow-hidden border border-slate-200 dark:border-slate-600">
                  {coverType === "color" ? (
                    <div className={cn("w-full h-full", color)} />
                  ) : (
                    <img
                      src={
                        customImageUrl.trim() || "/default-notebook-cover.svg"
                      }
                      alt="Cover preview"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.target.src = "/default-notebook-cover.svg";
                      }}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button
                type="button"
                variant="outline"
                className="border-slate-300 dark:border-slate-600"
              >
                Cancel
              </Button>
            </DialogClose>
            <Button
              type="submit"
              className="bg-primary hover:bg-primary/90 text-primary-foreground"
            >
              {notebookToEdit ? "Save Changes" : "Create Notebook"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default function NotebooksPage() {
  const [notebooks, setNotebooks] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("updated_at");
  const [sortOrder, setSortOrder] = useState("desc");
  const [filterBy, setFilterBy] = useState("all");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [notebookToEdit, setNotebookToEdit] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const { currentUser } = useAuth();

  useEffect(() => {
    const fetchNotebooks = async () => {
      if (!currentUser) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        let query = supabase
          .from("notebooks")
          .select("*")
          .eq("user_id", currentUser.id);

        // Apply sorting
        const ascending = sortOrder === "asc";
        query = query.order(sortBy, { ascending });

        const { data, error } = await query;

        if (error) throw error;
        setNotebooks(data || []);
      } catch (error) {
        console.error("Error fetching notebooks:", error);
        toast({
          title: "Error",
          description: "Failed to fetch notebooks: " + error.message,
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchNotebooks();
  }, [currentUser, isDialogOpen, sortBy, sortOrder, toast]);

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleOpenNewNotebookDialog = () => {
    setNotebookToEdit(null);
    setIsDialogOpen(true);
  };

  const handleOpenEditNotebookDialog = (notebook) => {
    setNotebookToEdit(notebook);
    setIsDialogOpen(true);
  };

  const handleDeleteNotebook = async (id) => {
    if (!currentUser) {
      toast({
        title: "Error",
        description: "You must be logged in to delete notebooks.",
        variant: "destructive",
      });
      return;
    }

    try {
      const { error } = await supabase
        .from("notebooks")
        .delete()
        .eq("id", id)
        .eq("user_id", currentUser.id);

      if (error) throw error;

      setNotebooks(notebooks.filter((nb) => nb.id !== id));
      toast({
        title: "Notebook Deleted",
        description: "The notebook has been deleted.",
      });
    } catch (error) {
      console.error("Error deleting notebook:", error);
      toast({
        title: "Error",
        description: "Failed to delete notebook: " + error.message,
        variant: "destructive",
      });
    }
  };

  const handleDialogSubmit = async (notebookData) => {
    if (!currentUser) {
      toast({
        title: "Error",
        description: "You must be logged in to create or edit notebooks.",
        variant: "destructive",
      });
      return;
    }

    try {
      if (notebookData.id) {
        // Update existing notebook
        const { error, data } = await supabase
          .from("notebooks")
          .update({
            title: notebookData.title,
            description: notebookData.description,
            tags: notebookData.tags,
            color: notebookData.color,
            cover_image:
              notebookData.cover_image || "/default-notebook-cover.svg",
            cover_type: notebookData.cover_type || "image",
            updated_at: new Date().toISOString(),
          })
          .eq("id", notebookData.id)
          .eq("user_id", currentUser.id)
          .select();

        if (error) throw error;

        setNotebooks(
          notebooks.map((nb) =>
            nb.id === notebookData.id ? { ...nb, ...data[0] } : nb
          )
        );
        toast({
          title: "Notebook Updated",
          description: `"${notebookData.title}" has been updated.`,
        });
      } else {
        // Create new notebook
        const { error, data } = await supabase
          .from("notebooks")
          .insert({
            title: notebookData.title,
            description: notebookData.description,
            tags: notebookData.tags,
            color: notebookData.color,
            cover_image:
              notebookData.cover_image || "/default-notebook-cover.svg",
            cover_type: notebookData.cover_type || "image",
            user_id: currentUser.id,
          })
          .select();

        if (error) throw error;

        setNotebooks([data[0], ...notebooks]);
        toast({
          title: "Notebook Created",
          description: `"${notebookData.title}" has been created.`,
        });
      }
    } catch (error) {
      console.error("Error saving notebook:", error);
      toast({
        title: "Error",
        description: "Failed to save notebook: " + error.message,
        variant: "destructive",
      });
    }
  };

  // Enhanced filtering and searching
  const filteredNotebooks = notebooks.filter((nb) => {
    const term = searchTerm.toLowerCase();

    // Search filter
    const matchesSearch =
      !term ||
      (nb.title && nb.title.toLowerCase().includes(term)) ||
      (nb.description && nb.description.toLowerCase().includes(term)) ||
      (nb.tags && nb.tags.some((tag) => tag.toLowerCase().includes(term))) ||
      (nb.created_at &&
        new Date(nb.created_at)
          .toLocaleDateString()
          .toLowerCase()
          .includes(term)) ||
      (nb.updated_at &&
        new Date(nb.updated_at)
          .toLocaleDateString()
          .toLowerCase()
          .includes(term));

    // Date filter
    const now = new Date();
    const notebookDate = new Date(nb.updated_at || nb.created_at);
    let matchesDateFilter = true;

    if (filterBy === "today") {
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      matchesDateFilter = notebookDate >= today;
    } else if (filterBy === "week") {
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      matchesDateFilter = notebookDate >= weekAgo;
    } else if (filterBy === "month") {
      const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      matchesDateFilter = notebookDate >= monthAgo;
    }

    return matchesSearch && matchesDateFilter;
  });

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="p-0 md:p-2"
    >
      <header className="mb-6 md:mb-8">
        <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
          <h1 className="text-3xl sm:text-4xl font-bold text-slate-800 dark:text-slate-100">
            Notebooks
          </h1>
          <Button
            onClick={handleOpenNewNotebookDialog}
            className="bg-primary hover:bg-primary/90 text-primary-foreground rounded-lg shadow-md"
          >
            <PlusCircle className="mr-2 h-5 w-5" /> New Notebook
          </Button>
        </div>
        <div className="mt-6 flex flex-col gap-4 p-4 bg-slate-100 dark:bg-slate-800/60 rounded-xl shadow">
          {/* Search Bar */}
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-slate-400 dark:text-slate-500" />
            <Input
              type="search"
              placeholder="Search notebooks by title, description, tags, date..."
              className="w-full pl-10 pr-4 py-2.5 rounded-lg bg-white dark:bg-slate-700/50 border-slate-300 dark:border-slate-600"
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </div>

          {/* Filters and Sorting */}
          <div className="flex flex-col sm:flex-row gap-3 items-center">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-slate-600 dark:text-slate-400" />
              <Select value={filterBy} onValueChange={setFilterBy}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Filter" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-slate-600 dark:text-slate-400" />
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-36">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="updated_at">Last Updated</SelectItem>
                  <SelectItem value="created_at">Date Created</SelectItem>
                  <SelectItem value="title">Title</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
              className="flex items-center gap-1"
            >
              {sortOrder === "asc" ? (
                <SortAsc className="h-4 w-4" />
              ) : (
                <SortDesc className="h-4 w-4" />
              )}
              {sortOrder === "asc" ? "Ascending" : "Descending"}
            </Button>
          </div>
        </div>
      </header>

      {isLoading ? (
        <div className="text-center py-10">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-slate-600 dark:text-slate-400">
            Loading notebooks...
          </p>
        </div>
      ) : filteredNotebooks.length === 0 ? (
        <div className="text-center py-10">
          <BookOpen className="mx-auto mb-4 h-16 w-16 text-slate-400" />
          <p className="text-xl text-slate-600 dark:text-slate-400 mb-2">
            {searchTerm || filterBy !== "all"
              ? "No notebooks match your search"
              : "No notebooks found"}
          </p>
          <p className="text-slate-500 dark:text-slate-500 mb-4">
            {searchTerm || filterBy !== "all"
              ? "Try adjusting your search terms or filters"
              : "Create your first notebook to organize your notes!"}
          </p>
          {!searchTerm && filterBy === "all" && (
            <Button onClick={handleOpenNewNotebookDialog} className="mt-2">
              <PlusCircle className="mr-2 h-4 w-4" />
              Create Notebook
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6">
          {filteredNotebooks.map((notebook) => (
            <NotebookCard
              key={notebook.id}
              notebook={notebook}
              onDelete={handleDeleteNotebook}
              onEdit={handleOpenEditNotebookDialog}
            />
          ))}
        </div>
      )}
      <NotebookDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        onSubmit={handleDialogSubmit}
        notebookToEdit={notebookToEdit}
      />
    </motion.div>
  );
}
