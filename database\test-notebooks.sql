-- Test queries to verify notebook functionality
-- Run these in Supabase SQL Editor to test the database setup

-- Test 1: Check if tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('notebooks', 'notes', 'user_profiles');

-- Test 2: Check table structure for notebooks
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'notebooks'
ORDER BY ordinal_position;

-- Test 3: Check table structure for notes
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'notes'
ORDER BY ordinal_position;

-- Test 4: Check RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies
WHERE tablename IN ('notebooks', 'notes');

-- Test 5: Check indexes
SELECT indexname, tablename, indexdef
FROM pg_indexes
WHERE tablename IN ('notebooks', 'notes')
AND schemaname = 'public';

-- Test 6: Insert a test notebook (replace 'your-user-id' with actual user ID)
-- INSERT INTO notebooks (title, description, tags, user_id)
-- VALUES ('Test Notebook', 'This is a test notebook', ARRAY['test', 'demo'], 'your-user-id');

-- Test 7: Insert a test note (replace IDs with actual values)
-- INSERT INTO notes (title, content_html, notebook_id, user_id)
-- VALUES ('Test Note', '<p>This is a test note</p>', 'notebook-id', 'your-user-id');

-- Test 8: Query notebooks with notes count
-- SELECT 
--   n.id,
--   n.title,
--   n.description,
--   n.tags,
--   n.created_at,
--   n.updated_at,
--   COUNT(notes.id) as note_count
-- FROM notebooks n
-- LEFT JOIN notes ON n.id = notes.notebook_id AND notes.is_trashed = false
-- WHERE n.user_id = 'your-user-id'
-- GROUP BY n.id, n.title, n.description, n.tags, n.created_at, n.updated_at
-- ORDER BY n.updated_at DESC;

-- Test 9: Search notebooks by title, description, or tags
-- SELECT * FROM notebooks 
-- WHERE user_id = 'your-user-id'
-- AND (
--   title ILIKE '%search-term%' OR 
--   description ILIKE '%search-term%' OR 
--   'search-term' = ANY(tags)
-- );

-- Test 10: Get notes in a specific notebook
-- SELECT 
--   id,
--   title,
--   content_html,
--   tags,
--   is_favorite,
--   is_locked,
--   is_sticky,
--   created_at,
--   updated_at
-- FROM notes 
-- WHERE notebook_id = 'notebook-id' 
-- AND user_id = 'your-user-id'
-- AND is_trashed = false
-- ORDER BY updated_at DESC;
