import React, { useState, useEffect } from "react";
import { useParams, useNavigate, useSearchParams } from "react-router-dom";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import {
  ArrowLeft,
  Plus,
  Search,
  Share,
  MoreVertical,
  FileText,
  Calendar,
  Star,
  Lock,
  Pin,
  Trash2,
  Edit3,
  SortAsc,
  SortDesc,
  Menu,
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { supabase } from "@/lib/supabaseClient";
import { cn } from "@/lib/utils";

export default function NotebookNotesPage() {
  const { notebookId } = useParams();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { toast } = useToast();
  const { currentUser } = useAuth();

  // State
  const [notebook, setNotebook] = useState(null);
  const [notes, setNotes] = useState([]);
  const [selectedNote, setSelectedNote] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("updated_at");
  const [sortOrder, setSortOrder] = useState("desc");
  const [isLoading, setIsLoading] = useState(true);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  // Fetch notebook and notes
  useEffect(() => {
    const fetchData = async () => {
      if (!currentUser || !notebookId) {
        setIsLoading(false);
        return;
      }

      try {
        // Fetch notebook
        const { data: notebookData, error: notebookError } = await supabase
          .from("notebooks")
          .select("*")
          .eq("id", notebookId)
          .single();

        if (notebookError) throw notebookError;
        setNotebook(notebookData);

        // Fetch notes - handle missing columns gracefully
        let notesQuery = supabase
          .from("notes")
          .select("*")
          .eq("notebook_id", notebookId)
          .eq("user_id", currentUser.id);

        // Only filter by is_trashed if the column exists
        // For now, we'll skip this filter to avoid errors

        const ascending = sortOrder === "asc";
        notesQuery = notesQuery.order(sortBy, { ascending });

        const { data: notesData, error: notesError } = await notesQuery;
        if (notesError) throw notesError;

        setNotes(notesData || []);

        // Auto-select first note or note from URL
        const noteIdFromUrl = searchParams.get("note");
        if (noteIdFromUrl) {
          const noteFromUrl = notesData?.find((n) => n.id === noteIdFromUrl);
          if (noteFromUrl) {
            setSelectedNote(noteFromUrl);
          }
        } else if (notesData && notesData.length > 0) {
          setSelectedNote(notesData[0]);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast({
          title: "Error",
          description: "Failed to load notebook data",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [notebookId, currentUser, sortBy, sortOrder, searchParams, toast]);

  // Filter notes based on search
  const filteredNotes = notes.filter((note) => {
    const term = searchTerm.toLowerCase();
    return (
      !term ||
      (note.title && note.title.toLowerCase().includes(term)) ||
      (note.content_html && note.content_html.toLowerCase().includes(term)) ||
      (note.content && note.content.toLowerCase().includes(term)) ||
      (note.tags &&
        Array.isArray(note.tags) &&
        note.tags.some((tag) => tag.toLowerCase().includes(term)))
    );
  });

  const handleCreateNote = async () => {
    if (!currentUser || !notebookId) {
      toast({
        title: "Error",
        description: "Missing user or notebook information",
        variant: "destructive",
      });
      return;
    }

    try {
      console.log(
        "Creating note for user:",
        currentUser.id,
        "in notebook:",
        notebookId
      );

      // Basic note data that should always work
      const noteData = {
        title: "Untitled",
        notebook_id: notebookId,
        user_id: currentUser.id,
      };

      // Add optional fields if they exist in the table
      try {
        noteData.content_html = "<p>Start writing...</p>";
        noteData.is_trashed = false;
        noteData.is_favorite = false;
        noteData.is_locked = false;
        noteData.is_sticky = false;
        noteData.tags = [];
      } catch (e) {
        console.log("Some optional fields not available:", e);
      }

      console.log("Note data:", noteData);

      const { data, error } = await supabase
        .from("notes")
        .insert(noteData)
        .select()
        .single();

      if (error) {
        console.error("Supabase error:", error);
        throw error;
      }

      console.log("Created note:", data);
      setNotes([data, ...notes]);
      setSelectedNote(data);

      toast({
        title: "Note Created",
        description: "New note has been created",
      });
    } catch (error) {
      console.error("Error creating note:", error);
      toast({
        title: "Error",
        description: `Failed to create note: ${error.message}`,
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return "Today";
    if (diffDays === 2) return "Yesterday";
    if (diffDays <= 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-slate-50 dark:bg-slate-900">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!notebook) {
    return (
      <div className="h-screen flex items-center justify-center bg-slate-50 dark:bg-slate-900">
        <div className="text-center">
          <FileText className="mx-auto h-16 w-16 text-slate-400 mb-4" />
          <h2 className="text-xl font-semibold text-slate-600 dark:text-slate-400 mb-2">
            Notebook not found
          </h2>
          <Button onClick={() => navigate("/notebooks")} variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Notebooks
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex bg-slate-50 dark:bg-slate-900 overflow-hidden">
      {/* Sidebar */}
      <div
        className={cn(
          "flex flex-col bg-slate-800 dark:bg-slate-950 text-white transition-all duration-300 border-r border-slate-700",
          isSidebarCollapsed ? "w-16" : "w-80"
        )}
      >
        {/* Header */}
        <div className="p-4 border-b border-slate-700 bg-slate-900">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
                className="text-slate-300 hover:text-white hover:bg-slate-700"
              >
                <Menu className="h-4 w-4" />
              </Button>
              {!isSidebarCollapsed && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate("/notebooks")}
                  className="text-slate-300 hover:text-white hover:bg-slate-700"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
              )}
            </div>
            {!isSidebarCollapsed && (
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-slate-300 hover:text-white hover:bg-slate-700"
                >
                  <Share className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-slate-300 hover:text-white hover:bg-slate-700"
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </div>
            )}
          </div>

          {!isSidebarCollapsed && (
            <>
              <h1 className="text-xl font-bold mb-1 text-white">
                {notebook.title}
              </h1>
              <p className="text-sm text-slate-400 mb-4">
                {filteredNotes.length}{" "}
                {filteredNotes.length === 1 ? "note" : "notes"}
              </p>

              {/* Search */}
              <div className="relative mb-4">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  placeholder="Search notes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:border-blue-500"
                />
              </div>

              {/* Sort Controls */}
              <div className="flex items-center gap-2 mb-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    setSortOrder(sortOrder === "asc" ? "desc" : "asc")
                  }
                  className="text-slate-300 hover:text-white hover:bg-slate-700"
                >
                  {sortOrder === "asc" ? (
                    <SortAsc className="h-4 w-4" />
                  ) : (
                    <SortDesc className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCreateNote}
                  className="text-slate-300 hover:text-white hover:bg-slate-700 flex-1"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  New Note
                </Button>
              </div>
            </>
          )}
        </div>

        {/* Notes List */}
        <div className="flex-1 overflow-y-auto">
          {!isSidebarCollapsed &&
            filteredNotes.map((note) => (
              <motion.div
                key={note.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                onClick={() => setSelectedNote(note)}
                className={cn(
                  "p-4 border-b border-slate-700 cursor-pointer hover:bg-slate-700 transition-all duration-200",
                  selectedNote?.id === note.id &&
                    "bg-slate-700 border-l-4 border-l-blue-500"
                )}
              >
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-medium text-white line-clamp-1 text-sm">
                    {note.title || "Untitled"}
                  </h3>
                  <div className="flex items-center gap-1 ml-2">
                    {note.is_favorite && (
                      <Star className="h-3 w-3 text-yellow-400 fill-current" />
                    )}
                    {note.is_locked && (
                      <Lock className="h-3 w-3 text-red-400" />
                    )}
                    {note.is_sticky && (
                      <Pin className="h-3 w-3 text-blue-400" />
                    )}
                  </div>
                </div>
                <p className="text-xs text-slate-300 line-clamp-2 mb-2 leading-relaxed">
                  {note.content_html
                    ? note.content_html
                        .replace(/<[^>]*>/g, "")
                        .substring(0, 80) + "..."
                    : note.content
                    ? note.content.substring(0, 80) + "..."
                    : "No content"}
                </p>
                <div className="flex items-center justify-between text-xs text-slate-400">
                  <span>{formatDate(note.updated_at)}</span>
                  {note.tags && note.tags.length > 0 && (
                    <span className="bg-slate-600 px-2 py-1 rounded text-xs">
                      {note.tags[0]}
                    </span>
                  )}
                </div>
              </motion.div>
            ))}

          {!isSidebarCollapsed && filteredNotes.length === 0 && (
            <div className="p-8 text-center">
              <FileText className="mx-auto h-12 w-12 text-slate-500 mb-4" />
              <p className="text-slate-400 mb-4 text-sm">
                {searchTerm
                  ? "No notes match your search"
                  : "No notes in this notebook"}
              </p>
              {!searchTerm && (
                <Button
                  onClick={handleCreateNote}
                  variant="outline"
                  size="sm"
                  className="border-slate-600 text-slate-300 hover:bg-slate-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Note
                </Button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col bg-white dark:bg-slate-800">
        {selectedNote ? (
          <>
            {/* Note Header */}
            <div className="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div>
                    <h2 className="text-2xl font-bold text-slate-800 dark:text-slate-200 mb-1">
                      {selectedNote.title || "Untitled"}
                    </h2>
                    <p className="text-sm text-slate-500 dark:text-slate-400">
                      Last edited on {formatDate(selectedNote.updated_at)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm">
                    <Share className="h-4 w-4 mr-2" />
                    Share
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigate(`/notes/${selectedNote.id}`)}
                  >
                    <Edit3 className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                </div>
              </div>
            </div>

            {/* Note Content */}
            <div className="flex-1 p-8 bg-white dark:bg-slate-800 overflow-y-auto">
              <div className="max-w-4xl mx-auto">
                <div
                  className="prose prose-lg dark:prose-invert max-w-none leading-relaxed"
                  dangerouslySetInnerHTML={{
                    __html:
                      selectedNote.content_html ||
                      (selectedNote.content
                        ? `<p>${selectedNote.content}</p>`
                        : null) ||
                      '<p class="text-slate-500">This note is empty. Click Edit to start writing...</p>',
                  }}
                />
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center bg-white dark:bg-slate-800">
            <div className="text-center max-w-md">
              <FileText className="mx-auto h-20 w-20 text-slate-400 mb-6" />
              <h2 className="text-2xl font-semibold text-slate-600 dark:text-slate-400 mb-3">
                Select a note to view
              </h2>
              <p className="text-slate-500 dark:text-slate-500 mb-6 leading-relaxed">
                Choose a note from the sidebar to read its content, or create a
                new note to get started.
              </p>
              <Button onClick={handleCreateNote} size="lg">
                <Plus className="h-5 w-5 mr-2" />
                Create New Note
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
