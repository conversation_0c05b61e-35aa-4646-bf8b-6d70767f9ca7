-- Updated Notebooks Schema with all required fields
-- Run this in Supabase SQL Editor

-- Drop existing table if you want to recreate (WARNING: This will delete existing data)
-- DROP TABLE IF EXISTS notebooks CASCADE;

-- Create or update notebooks table with all required fields
CREATE TABLE IF NOT EXISTS notebooks (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    cover_image_url TEXT,
    tags TEXT[] DEFAULT '{}',
    is_archived BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    shared_with <PERSON>UI<PERSON>[] DEFAULT '{}',
    permission_level TEXT DEFAULT 'private' CHECK (permission_level IN ('private', 'view', 'edit', 'public')),
    
    -- Legacy fields for backward compatibility
    color VARCHAR(50) DEFAULT 'bg-blue-500',
    cover_image TEXT DEFAULT '/default-notebook-cover.svg',
    cover_type VARCHAR(20) DEFAULT 'image'
);

-- Add columns if they don't exist (for existing tables)
ALTER TABLE notebooks ADD COLUMN IF NOT EXISTS cover_image_url TEXT;
ALTER TABLE notebooks ADD COLUMN IF NOT EXISTS is_archived BOOLEAN DEFAULT FALSE;
ALTER TABLE notebooks ADD COLUMN IF NOT EXISTS shared_with UUID[] DEFAULT '{}';
ALTER TABLE notebooks ADD COLUMN IF NOT EXISTS permission_level TEXT DEFAULT 'private';

-- Add constraint for permission_level if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'notebooks_permission_level_check'
    ) THEN
        ALTER TABLE notebooks ADD CONSTRAINT notebooks_permission_level_check 
        CHECK (permission_level IN ('private', 'view', 'edit', 'public'));
    END IF;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notebooks_user_id ON notebooks(user_id);
CREATE INDEX IF NOT EXISTS idx_notebooks_updated_at ON notebooks(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_notebooks_is_archived ON notebooks(is_archived);
CREATE INDEX IF NOT EXISTS idx_notebooks_permission_level ON notebooks(permission_level);
CREATE INDEX IF NOT EXISTS idx_notebooks_tags ON notebooks USING GIN(tags);

-- Enable RLS
ALTER TABLE notebooks ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view their own notebooks" ON notebooks;
DROP POLICY IF EXISTS "Users can insert their own notebooks" ON notebooks;
DROP POLICY IF EXISTS "Users can update their own notebooks" ON notebooks;
DROP POLICY IF EXISTS "Users can delete their own notebooks" ON notebooks;

-- Create comprehensive RLS policies
CREATE POLICY "Users can view their own notebooks" ON notebooks
    FOR SELECT USING (
        auth.uid() = user_id OR 
        auth.uid() = ANY(shared_with) OR 
        permission_level = 'public'
    );

CREATE POLICY "Users can insert their own notebooks" ON notebooks
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own notebooks" ON notebooks
    FOR UPDATE USING (
        auth.uid() = user_id OR 
        (auth.uid() = ANY(shared_with) AND permission_level = 'edit')
    );

CREATE POLICY "Users can delete their own notebooks" ON notebooks
    FOR DELETE USING (auth.uid() = user_id);

-- Create function for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for automatic timestamp updates
DROP TRIGGER IF EXISTS update_notebooks_updated_at ON notebooks;
CREATE TRIGGER update_notebooks_updated_at
    BEFORE UPDATE ON notebooks
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Verify the table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'notebooks'
ORDER BY ordinal_position;
