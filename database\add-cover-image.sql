-- Add cover_image field to notebooks table
-- Run this in Supabase SQL Editor to add cover image functionality

-- Add cover_image column to notebooks table
ALTER TABLE notebooks
ADD COLUMN IF NOT EXISTS cover_image TEXT DEFAULT '/default-notebook-cover.svg';

-- Add cover_type column to track whether using image or color
ALTER TABLE notebooks
ADD COLUMN IF NOT EXISTS cover_type VARCHAR(20) DEFAULT 'image';

-- Update existing notebooks to have the default cover image
UPDATE notebooks
SET cover_image = '/default-notebook-cover.svg', cover_type = 'image'
WHERE cover_image IS NULL OR cover_image = '';

-- Verify the change
SELECT id, title, cover_image, cover_type, color FROM notebooks LIMIT 5;
