-- Add cover_image field to notebooks table
-- Run this in Supabase SQL Editor to add cover image functionality

-- Add cover_image column to notebooks table
ALTER TABLE notebooks
ADD COLUMN IF NOT EXISTS cover_image TEXT DEFAULT '/default-notebook-cover.svg';

-- Update existing notebooks to have the default cover image
UPDATE notebooks
SET cover_image = '/default-notebook-cover.svg'
WHERE cover_image IS NULL OR cover_image = '';

-- Verify the change
SELECT id, title, cover_image FROM notebooks LIMIT 5;
