-- Debug script to check database tables and permissions
-- Run this in Supabase SQL Editor to diagnose issues

-- 1. Check if tables exist
SELECT 'Tables that exist:' as info;
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('notebooks', 'notes', 'user_profiles');

-- 2. Check notebooks table structure
SELECT 'Notebooks table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'notebooks'
ORDER BY ordinal_position;

-- 3. Check notes table structure
SELECT 'Notes table structure:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'notes'
ORDER BY ordinal_position;

-- 4. Check RLS policies
SELECT 'RLS Policies:' as info;
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies
WHERE tablename IN ('notebooks', 'notes');

-- 5. Check if RLS is enabled
SELECT 'RLS Status:' as info;
SELECT schemaname, tablename, rowsecurity
FROM pg_tables
WHERE tablename IN ('notebooks', 'notes');

-- 6. Test basic insert (replace 'your-user-id' with actual user ID from auth.users)
-- First, let's see what users exist
SELECT 'Available users:' as info;
SELECT id, email FROM auth.users LIMIT 5;

-- 7. Test notebook insert with minimal data
-- UNCOMMENT AND REPLACE 'your-user-id' with actual user ID:
-- INSERT INTO notebooks (title, user_id) 
-- VALUES ('Test Notebook', 'your-user-id');

-- 8. Test note insert with minimal data
-- UNCOMMENT AND REPLACE IDs with actual values:
-- INSERT INTO notes (title, user_id, notebook_id) 
-- VALUES ('Test Note', 'your-user-id', 'notebook-id');

-- 9. Check current data
SELECT 'Current notebooks:' as info;
SELECT id, title, user_id, created_at FROM notebooks LIMIT 5;

SELECT 'Current notes:' as info;
SELECT id, title, user_id, notebook_id, created_at FROM notes LIMIT 5;
