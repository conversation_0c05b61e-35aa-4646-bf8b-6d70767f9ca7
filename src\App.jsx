import React, { Suspense, lazy } from "react";
import {
  BrowserRouter,
  Routes,
  Route,
  Navigate,
  Outlet,
} from "react-router-dom";
import { Toaster } from "@/components/ui/toaster";
import { AuthProvider, useAuth } from "@/contexts/AuthContext";
import AppLayout from "@/components/layout/AppLayout";
import WelcomePage from "@/pages/WelcomePage";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import LoadingSpinner from "@/components/layout/LoadingSpinner";

const LoginPage = lazy(() => import("@/pages/LoginPage"));
const SignUpPage = lazy(() => import("@/pages/SignUpPage"));
const DashboardPage = lazy(() => import("@/pages/DashboardPage"));
const NotesPage = lazy(() => import("@/pages/NotesPage"));
const NewNotePage = lazy(() => import("@/pages/NewNotePage"));
const NotebooksPage = lazy(() => import("@/pages/NotebooksPage"));
const NotebookNotesPage = lazy(() => import("@/pages/NotebookNotesPage"));
const AiFeaturesPage = lazy(() => import("@/pages/AiFeaturesPage"));
const FlashcardGeneratorPage = lazy(() =>
  import("@/pages/FlashcardGeneratorPage")
);
const AIDnoteAssistant = lazy(() => import("@/pages/DNoteAssistant"));
const CalendarPage = lazy(() => import("@/pages/CalendarPage"));
const RemindersPage = lazy(() => import("@/pages/RemindersPage"));
const TagsPage = lazy(() => import("@/pages/TagsPage"));
const FilesPage = lazy(() => import("@/pages/FilesPage"));
const TrashPage = lazy(() => import("@/pages/TrashPage"));
const SettingsPage = lazy(() => import("@/pages/SettingsPage"));
const FavoritesPage = lazy(() => import("@/pages/FavoritesPage"));
const TasksPage = lazy(() => import("@/pages/TasksPage"));
const VoiceToTextPage = lazy(() => import("@/pages/VoiceToTextPage"));
const StudyModePage = lazy(() => import("@/pages/StudyModePage"));
const PdfImageAnnotationPage = lazy(() =>
  import("@/pages/PdfImageAnnotationPage")
);
const NotificationsPage = lazy(() => import("@/pages/NotificationsPage"));
const CustomizePage = lazy(() => import("@/pages/CustomizePage"));
const TranslationPage = lazy(() => import("@/pages/TranslationPage"));

function ProtectedRouteWrapper() {
  const { currentUser, loading } = useAuth();
  if (loading) {
    // The LoadingSpinner is now a full-page overlay by default
    return <LoadingSpinner message="Authenticating D-Note..." />;
  }
  if (!currentUser) {
    return <Navigate to="/login" replace />;
  }
  // If currentUser exists, AppLayout will render the Outlet for protected routes
  return (
    <AppLayout>
      <Outlet />
    </AppLayout>
  );
}
const GenericPlaceholderPage = ({ title, message, showImage = true }) => (
  <div className="p-6 flex flex-col items-center justify-center min-h-[calc(100vh-200px)] text-center">
    <Card className="max-w-lg w-full p-8 bg-card shadow-xl">
      <h1 className="text-3xl font-bold mb-4 text-card-foreground">{title}</h1>
      <p className="text-muted-foreground mb-6">
        {message ||
          "This page is under construction or the content you're looking for isn't available right now. Please check back later!"}
      </p>
      {showImage && (
        <img
          alt="Illustration indicating page content is unavailable or under construction"
          className="mt-6 mx-auto w-full max-w-xs opacity-70"
          src="https://images.unsplash.com/photo-1578811344633-3376eab0f8ce"
        />
      )}
      <Button asChild className="mt-8">
        <Link to="/dashboard">Go to Dashboard</Link>
      </Button>
    </Card>
  </div>
);

function App() {
  return (
    <BrowserRouter>
      <AuthProvider>
        <Suspense fallback={<LoadingSpinner />}>
          <Routes>
            {/* Public routes */}
            <Route path="/welcome" element={<WelcomePage />} />
            <Route path="/login" element={<LoginPage />} />
            <Route path="/signup" element={<SignUpPage />} />
            <Route
              path="/forgot-password"
              element={
                <GenericPlaceholderPage
                  title="Forgot Password"
                  message="Password recovery options will be available here soon."
                />
              }
            />
            {/* Add other public utility pages here if needed */}

            {/* Protected routes */}
            <Route element={<ProtectedRouteWrapper />}>
              <Route path="/dashboard" element={<DashboardPage />} />
              <Route path="/notes" element={<NotesPage />} />
              <Route path="/notes/new" element={<NewNotePage />} />
              <Route path="/notes/:noteId" element={<NewNotePage />} />
              <Route path="/notebooks" element={<NotebooksPage />} />
              <Route path="/notebooks/:notebookId" element={<NotesPage />} />
              <Route
                path="/notebooks/:notebookId/notes"
                element={<NotebookNotesPage />}
              />

              <Route path="/ai-features" element={<AiFeaturesPage />} />
              <Route
                path="/ai-features/flashcard-generator"
                element={<FlashcardGeneratorPage />}
              />
              <Route path="/assistant" element={<AIDnoteAssistant />} />
              <Route
                path="/ai-features/translate"
                element={<TranslationPage />}
              />

              <Route path="/calendar" element={<CalendarPage />} />
              <Route path="/reminders" element={<RemindersPage />} />
              <Route path="/tags" element={<TagsPage />} />
              <Route path="/files" element={<FilesPage />} />
              <Route path="/trash" element={<TrashPage />} />

              <Route path="/settings" element={<SettingsPage />} />
              <Route
                path="/settings/profile"
                element={<GenericPlaceholderPage title="Profile Settings" />}
              />
              <Route
                path="/settings/account"
                element={<GenericPlaceholderPage title="Account Settings" />}
              />
              <Route
                path="/settings/preferences"
                element={<GenericPlaceholderPage title="Preferences" />}
              />
              <Route
                path="/settings/security"
                element={<GenericPlaceholderPage title="Security Settings" />}
              />

              <Route path="/favorites" element={<FavoritesPage />} />
              <Route path="/tasks" element={<TasksPage />} />
              <Route path="/voice-to-text" element={<VoiceToTextPage />} />
              <Route path="/study-mode" element={<StudyModePage />} />
              <Route
                path="/pdf-image-annotation"
                element={<PdfImageAnnotationPage />}
              />
              <Route path="/notifications" element={<NotificationsPage />} />
              <Route path="/customize" element={<CustomizePage />} />

              {/* Default redirect for authenticated users */}
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              {/* Catch-all for authenticated space */}
              <Route
                path="*"
                element={
                  <GenericPlaceholderPage
                    title="404 - Page Not Found"
                    message="Oops! The page you're looking for doesn't seem to exist within the app."
                  />
                }
              />
            </Route>

            {/* Fallback for any other unmatched routes (could be a global 404 or redirect to welcome) */}
            <Route path="*" element={<Navigate to="/welcome" replace />} />
          </Routes>
        </Suspense>
        <Toaster />
      </AuthProvider>
    </BrowserRouter>
  );
}

export default App;
